# ==========================================================
# Codemagic 自动构建配置
# 适用：Flutter + iOS/Android 双端，iOS 使用 Codemagic 手动签名
# ==========================================================

# -------- 全局私密变量 --------------------------
environment:
  groups:
    - Debug                             # 含 PGYER_API_KEY
  vars:
    DEVELOPMENT_TEAM: VM8EAMJRY8        # Apple 开发者账号 Team ID
    BUNDLE_IDENTIFIER: com.jiyoujiaju.jijiahui

# ==========================================================
workflows:

  # ========== iOS Release ==========
  ios-release:
    name: iOS Release
    instance_type: mac_mini_m2
    max_build_duration: 90

    # 添加tag触发器
    triggering:
      events:
        - tag
      tag_patterns:
        - pattern: 'v*'
          include: true
        - pattern: '*-ios'
          include: true

    environment:
      groups:
        - Debug
      flutter: stable
      xcode: 16.4
      node: 18
      ruby: default
      ios_signing:
        provisioning_profiles:
          - adhoc                      # 在 Codemagic UI 中配置的描述文件引用名称
        certificates:
          - ios_distribution           # 在 Codemagic UI 中配置的证书引用名称
      vars:
        DEVELOPMENT_TEAM: $DEVELOPMENT_TEAM
        BUNDLE_IDENTIFIER: $BUNDLE_IDENTIFIER   # 不再声明 PGYER_API_KEY

    cache:
      cache_paths:
        - $HOME/Library/Caches/CocoaPods
        - $HOME/.pub-cache
        - ios/Pods

    scripts:
      - name: "🔍  Show environment"
        script: |
          set -e
          flutter --version
          xcodebuild -version
          pod --version

      - name: "📦  Flutter pub get"
        script: flutter pub get

      - name: "📦  CocoaPods install"
        script: |
          pod repo remove tuya-tuya-pod-specs || true
          if ! pod repo list | grep -q 'tuya-tuyapublicspecs'; then
            pod repo add tuya-tuyapublicspecs https://github.com/tuya/TuyaPublicSpecs.git
          fi
          cd ios
          pod install --repo-update --silent
          cd ..

      - name: "🔑  Write signing profiles"
        script: xcode-project use-profiles

      - name: "🛠️  Build IPA (signed)"
        script: |
          flutter build ipa \
            --release \
            --export-options-plist=/Users/<USER>/export_options.plist

      - name: "☁️  Upload to Pgyer"
        script: |
          set -e
          if [ -z "$PGYER_API_KEY" ]; then
            echo "❌ Error: PGYER_API_KEY is empty or not set"
            env | grep PGYER || true
            exit 1
          fi
          echo "✅ PGYER_API_KEY length: ${#PGYER_API_KEY}"
          echo "✅ PGYER_API_KEY preview: ${PGYER_API_KEY:0:4}******"
          IPA=$(find build/ios/ipa -name "*.ipa" | head -1)
          [ -f "$IPA" ] || { echo "❌ IPA not found"; exit 1; }
          echo "📤 Uploading $IPA to Pgyer ..."
          
          # 上传到蒲公英并获取响应
          RESPONSE=$(curl -F "file=@${IPA}" \
               -F "_api_key=${PGYER_API_KEY}" \
               -F "buildUpdateDescription=iOS 自动构建 $(date +'%F %T')" \
               https://www.pgyer.com/apiv2/app/upload -w "\nHTTP_CODE:%{http_code}\n")
          
          echo "$RESPONSE"
          
          # 设置安装链接环境变量（固定链接）
          echo "PGYER_INSTALL_URL=https://www.pgyer.com/Rmz9GHNj" >> $CM_ENV
          
          # 创建 release notes 文件（会包含在邮件中）
          cat > release_notes.txt <<EOF
          🎉 iOS 版本构建完成！

          📱 应用名称: 极家汇
          🔗 蒲公英安装链接: https://www.pgyer.com/Rmz9GHNj
          📲 请使用手机扫码或点击链接安装最新测试版本

          ✅ 构建时间: $(date +'%F %T')
          🏷️ 版本信息: iOS 自动构建
          EOF

    artifacts:
      - build/ios/ipa/*.ipa
      - /tmp/xcodebuild_logs/*.log

    # 添加邮件通知
    publishing:
      email:
        recipients:
          - <EMAIL>
          - <EMAIL>
        notify:
          success: true
          failure: false

  # ==========================================================
  # ========== Android Release ==========
  android-release:
    name: Android Release
    instance_type: mac_mini_m2          # 8 GB 机型足够
    max_build_duration: 90              # Codemagic 免费额度上限

    # 添加tag触发器
    triggering:
      events:
        - tag
      tag_patterns:
        - pattern: 'v*'
          include: true
        - pattern: '*-android'
          include: true

    environment:
      groups:
        - Debug                         # PGYER_API_KEY、KEYSTORE_PASSWORD 等放在 Codemagic UI
      flutter: stable
      java: 17
      node: 18
      vars:
        # 减少内存使用，避免重复输出
        JAVA_TOOL_OPTIONS: "-Xmx3g -XX:+UseG1GC"

    # 优化缓存配置，只缓存必要的依赖文件
    cache:
      cache_paths:
        - $HOME/.pub-cache
        - $HOME/.gradle/caches/modules-2/files-2.1
        - $HOME/.gradle/caches/modules-2/metadata-2.*/

    scripts:
      # 1) 安装缺失的 Android SDK 组件
      - name: "🔧  Install Android SDK Build-Tools 34.0.0"
        script: |
          set -e
          echo "📱 检查并安装 Android SDK Build-Tools 34.0.0..."
          
          # 使用 flutter 提供的 sdkmanager
          flutter doctor -v
          
          # 尝试使用 flutter 安装 Android SDK 组件
          echo "📦 通过 Flutter 安装 Android SDK 组件..."
          
          # 强制接受所有许可证
          if [ -n "$ANDROID_SDK_ROOT" ]; then
            export ANDROID_HOME=$ANDROID_SDK_ROOT
            export PATH=$PATH:$ANDROID_HOME/cmdline-tools/latest/bin
            
            echo "📦 接受所有 Android SDK 许可证..."
            yes | sdkmanager --licenses || true
            
            echo "📦 使用 sdkmanager 安装..."
            sdkmanager --update || true
            sdkmanager "build-tools;34.0.0" || sdkmanager "build-tools;33.0.2"
            sdkmanager "platforms;android-34" || sdkmanager "platforms;android-33"
            sdkmanager "platform-tools"
          fi
          
          # 备用方案：使用 flutter doctor 接受许可证
          flutter doctor --android-licenses || true
          
          echo "✅ Android SDK 组件安装完成"

      # 2) 基础信息
      - name: "🔍  Show environment"
        script: |
          set -e
          echo "构建开始时间: $(date)"
          flutter --version
          java -version
          gradle --version
          echo "Android SDK 路径: $ANDROID_SDK_ROOT"
          ls -la $ANDROID_SDK_ROOT/build-tools/ || echo "build-tools 目录不存在"

      # 3) Flutter 依赖
      - name: "📦  Flutter pub get"
        script: |
          set -e
          flutter pub get

      # 4) Gradle 内存/性能轻量优化
      - name: "⚙️  Configure Gradle properties"
        script: |
          set -e
          GRADLE_PROPS="android/gradle.properties"

          echo "🔧 备份并重写 gradle.properties 文件 (轻量版)..."
          cp "$GRADLE_PROPS" "${GRADLE_PROPS}.original" || true

          cat > "$GRADLE_PROPS" <<'EOF'
          # ====== Codemagic CI 优化 (轻量) ======
          org.gradle.jvmargs=-Xmx3g -Xms512m -XX:+UseG1GC
          org.gradle.daemon=false
          org.gradle.parallel=false
          org.gradle.caching=true
          org.gradle.workers.max=1
          org.gradle.logging.level=lifecycle

          # ====== Android 配置 ======
          android.useAndroidX=true
          android.enableJetifier=true

          # ====== 必需的项目属性 ======
          bizBomVersion=6.0.0
          sdkVersion=6.2.2

          # ====== 网络配置 ======
          systemProp.https.protocols=TLSv1.2,TLSv1.3
          systemProp.http.keepAlive=true
          systemProp.http.maxConnections=10
          systemProp.http.maxConnectionsPerRoute=10
          EOF

          echo "✅ gradle.properties 配置完成"

      # 5) 清理
      - name: "🧹  Flutter clean"
        script: |
          set -e
          flutter clean

      # 6) **关键**：确保 Gradle wrapper 指向 8.10.2
      - name: "🔧  Ensure Gradle wrapper 8.10.2"
        script: |
          set -e
          cd android
          TARGET_VERSION=8.10.2
          PROP_FILE="gradle/wrapper/gradle-wrapper.properties"

          mkdir -p gradle/wrapper

          if [ ! -f "./gradlew" ]; then
            echo "⚠️  gradlew 不存在，创建 wrapper ..."
            echo "distributionBase=GRADLE_USER_HOME" > "$PROP_FILE"
            echo "distributionPath=wrapper/dists" >> "$PROP_FILE"
            echo "zipStoreBase=GRADLE_USER_HOME" >> "$PROP_FILE"
            echo "zipStorePath=wrapper/dists" >> "$PROP_FILE"
            echo "distributionUrl=https\://services.gradle.org/distributions/gradle-${TARGET_VERSION}-all.zip" >> "$PROP_FILE"
            curl -s https://raw.githubusercontent.com/gradle/gradle/v${TARGET_VERSION}/gradlew > gradlew
            curl -s https://raw.githubusercontent.com/gradle/gradle/v${TARGET_VERSION}/gradlew.bat > gradlew.bat
            curl -s https://raw.githubusercontent.com/gradle/gradle/v${TARGET_VERSION}/gradle/wrapper/gradle-wrapper.jar > gradle/wrapper/gradle-wrapper.jar
            chmod +x ./gradlew
          else
            sed -i.bak -E "s#distributionUrl=.*#distributionUrl=https\\://services.gradle.org/distributions/gradle-${TARGET_VERSION}-all.zip#" "$PROP_FILE"
            chmod +x ./gradlew
          fi

          echo "✅ Gradle wrapper 配置完成"
          ./gradlew --version | head -5
          cd ..

      # 7) 构建签名 APK
      - name: "🛠️  Build signed APK"
        script: |
          set -e
          cd android
          ./gradlew --no-daemon --quiet tasks > /dev/null  # 预热，静默输出
          cd ..

          flutter build apk \
            --release \
            --target-platform android-arm64       # 已在 build.gradle 禁用混淆，无需 --no-shrink

      # 8) 上传到蒲公英
      - name: "☁️  Upload to Pgyer"
        script: |
          set -e
          if [ -z "$PGYER_API_KEY" ]; then
            echo "❌ PGYER_API_KEY 未配置"; exit 1
          fi
          APK=$(find build/app/outputs/flutter-apk -name "*.apk" | head -1)
          [ -f "$APK" ] || { echo "❌ 找不到 APK"; exit 1; }
          echo "📤 上传 $APK ..."
          
          # 上传到蒲公英并获取响应
          RESPONSE=$(curl -F "file=@${APK}" \
               -F "_api_key=${PGYER_API_KEY}" \
               -F "buildUpdateDescription=Android 自动构建 $(date +'%F %T')" \
               https://www.pgyer.com/apiv2/app/upload -w "\nHTTP_CODE:%{http_code}\n")
          
          echo "$RESPONSE"
          
          # 设置安装链接环境变量（固定链接）
          echo "PGYER_INSTALL_URL=https://www.pgyer.com/bp82mcrK" >> $CM_ENV
          
          # 创建 release notes 文件（会包含在邮件中）
          cat > release_notes.txt <<EOF
          🎉 Android 版本构建完成！

          📱 应用名称: 极家汇
          🔗 蒲公英安装链接: https://www.pgyer.com/bp82mcrK
          📲 请使用手机扫码或点击链接安装最新测试版本

          ✅ 构建时间: $(date +'%F %T')
          🏷️ 版本信息: Android 自动构建
          EOF

    artifacts:
      - build/app/outputs/flutter-apk/*.apk

    # 添加邮件通知
    publishing:
      email:
        recipients:
          - <EMAIL>
          - <EMAIL>
        notify:
          success: true
          failure: false
