import Flutter
import UIKit
import UserNotifications
import jpush_flutter
@main
@objc class AppDelegate: FlutterAppDelegate, CLLocationManagerDelegate {
    private let tuyaAppkey = "ktuupjsdgaheh7gtxhfn"
    private let tuyaSecretKey = "u73nktdvmrxd979aexxx5ug9khum9vhc"
    lazy var flutterEngine: FlutterEngine? = FlutterEngine(name: "my flutter engine")

    var navigationChannel: FlutterMethodChannel?
    
    override func application(
        _ application: UIApplication,
        didFinishLaunchingWithOptions launchOptions: [UIApplication.LaunchOptionsKey: Any]?
    ) -> Bool {

        guard let engine = flutterEngine else { return false }
        engine.run()
        
        FlutterUserPlugin.register(with: engine.registrar(forPlugin: "FlutterUserPlugin")!)
        GeneratedPluginRegistrant.register(with: engine)
        let flutterViewController = FlutterViewController(engine: engine, nibName: nil, bundle: nil)

        // 爱家
        if let registrar = engine.registrar(forPlugin: "native_ios_smartlife") {
            let factory = FLNativeViewFactory(messenger: registrar.messenger(), flutterViewController: flutterViewController)
            registrar.register(factory, withId: "native_ios_smartlife")
        }
        

        DispatchQueue.global().async {
            ThingSmartSDK.sharedInstance().start(withAppKey: self.tuyaAppkey, secretKey: self.tuyaSecretKey)
#if DEBUG
            ThingSmartSDK.sharedInstance().debugMode = true
#endif
        }

        // 创建导航控制器并隐藏导航栏
        let navigationController = UINavigationController(rootViewController: flutterViewController)
        navigationController.navigationBar.isHidden = true  // 默认隐藏导航栏

        // 配置导航方法通道
        navigationChannel = FlutterMethodChannel(name: "com.smartlife.navigation",
                                                 binaryMessenger: flutterViewController.binaryMessenger)
        
        navigationChannel?.setMethodCallHandler { [weak navigationController] (call, result) in
            switch call.method {
            case "showNavigationBar":
                navigationController?.navigationBar.isHidden = false
                result(nil)
            case "hideNavigationBar":
                navigationController?.navigationBar.isHidden = true
                result(nil)
            case "popToFlutter":
                navigationController?.popViewController(animated: true)
                result(nil)
            default:
                result(FlutterMethodNotImplemented)
            }
        }
        
        // 登录消息通道
        let loginChannel = FlutterMethodChannel(name: "com.smartlife.app/login",
                                                binaryMessenger: flutterViewController.binaryMessenger)

        loginChannel.setMethodCallHandler { (call: FlutterMethodCall, result: @escaping FlutterResult) in
            switch call.method {
            case "tuyaLogin":
                guard let args = call.arguments as? [String: String],
                      let mobile = args["mobile"],
                      let password = args["password"] else {
                    result(FlutterError(code: "INVALID_ARGUMENTS",
                                        message: "Invalid arguments",
                                        details: nil))
                    return
                }
                
                // 执行涂鸦登录
                ThingSmartUser.sharedInstance().login(byPhone: "86",
                                                     phoneNumber: mobile,
                                                     password: password,
                                                     success: {
                    print("tuya--login success")
                    NotificationCenter.default.post(name: NSNotification.Name(rawValue: "loginStatus"),
                                                    object: nil)
                    result(true)
                }, failure: { (error) in
                    if let e = error {
                        print("tuya--login failure: \(e)")
                        result(FlutterError(code: "LOGIN_FAILED",
                                            message: e.localizedDescription,
                                            details: nil))
                    }
                })
                
            case "tuyaLogout":
                // 执行涂鸦退出登录
                ThingSmartUser.sharedInstance().loginOut({
                    print("涂鸦登出成功")
                    NotificationCenter.default.post(name: NSNotification.Name(rawValue: "logoutStatus"),
                                                    object: nil)
                    result(true)
                }, failure: { (error) in
                    if let e = error {
                        print("涂鸦登出失败: \(e)")
                        result(FlutterError(code: "LOGOUT_FAILED",
                                            message: e.localizedDescription,
                                            details: nil))
                    }
                })
                
            default:
                result(FlutterMethodNotImplemented)
            }
        }
        

        window = UIWindow(frame: UIScreen.main.bounds)
        window?.backgroundColor = UIColor.white
        window?.rootViewController = navigationController
        window?.makeKeyAndVisible()

        // 初始化极光推送
        setupJPush(launchOptions: launchOptions)

        return super.application(application, didFinishLaunchingWithOptions: launchOptions)
    }
    
    // 添加一个方法以便其他类可以调用 popToFlutter
    func popToFlutter() {
        navigationChannel?.invokeMethod("popToFlutter", arguments: nil)
    }
    
    // 添加一个方法来获取或创建 FlutterViewController
    func getRootFlutterViewController() -> FlutterViewController {
        if let rootVC = window?.rootViewController as? UINavigationController,
           let flutterVC = rootVC.viewControllers.first as? FlutterViewController {
            return flutterVC
        }
        // 如果没有找到，创建一个新的
        return FlutterViewController(engine: flutterEngine!, nibName: nil, bundle: nil)
    }
    
    func navigateToFlutterRoute(_ route: String) {
        if let channel = navigationChannel {
            channel.invokeMethod(route, arguments: nil)
        }
    }

    // MARK: - 极光推送相关方法
    private func setupJPush(launchOptions: [UIApplication.LaunchOptionsKey: Any]?) {
        // 请求通知权限
        let center = UNUserNotificationCenter.current()
        center.delegate = self
        center.requestAuthorization(options: [.alert, .badge, .sound]) { granted, error in
            if granted {
                print("极光推送：通知权限获取成功")
            } else {
                print("极光推送：通知权限获取失败: \(error?.localizedDescription ?? "未知错误")")
            }
        }

        // 注册远程通知
        UIApplication.shared.registerForRemoteNotifications()

        // 初始化极光推送 - 使用 jpush_flutter 插件的方式
        print("极光推送：使用 jpush_flutter 插件进行初始化")
        // jpush_flutter 插件会自动处理初始化，这里只需要设置通知权限
    }

    // MARK: - 远程通知相关方法
    override func application(_ application: UIApplication, didRegisterForRemoteNotificationsWithDeviceToken deviceToken: Data) {
        // 注册 DeviceToken - jpush_flutter 插件会自动处理
        let tokenString = deviceToken.map { String(format: "%02.2hhx", $0) }.joined()
        print("极光推送：DeviceToken 注册成功: \(tokenString)")

        // 调用父类方法，让 jpush_flutter 插件处理
        super.application(application, didRegisterForRemoteNotificationsWithDeviceToken: deviceToken)
    }

    override func application(_ application: UIApplication, didFailToRegisterForRemoteNotificationsWithError error: Error) {
        print("极光推送：DeviceToken 注册失败: \(error.localizedDescription)")
    }

    // MARK: - UNUserNotificationCenterDelegate
    // 前台收到通知
    override func userNotificationCenter(_ center: UNUserNotificationCenter, willPresent notification: UNNotification, withCompletionHandler completionHandler: @escaping (UNNotificationPresentationOptions) -> Void) {
        let userInfo = notification.request.content.userInfo

        if let trigger = notification.request.trigger as? UNPushNotificationTrigger {
            // 远程通知
            print("极光推送：前台收到远程通知: \(userInfo)")
        } else {
            // 本地通知
            print("极光推送：前台收到本地通知: \(userInfo)")
        }

        // 前台显示通知
        completionHandler([.alert, .badge, .sound])
    }

    // 点击通知
    override func userNotificationCenter(_ center: UNUserNotificationCenter, didReceive response: UNNotificationResponse, withCompletionHandler completionHandler: @escaping () -> Void) {
        let userInfo = response.notification.request.content.userInfo

        if let trigger = response.notification.request.trigger as? UNPushNotificationTrigger {
            // 远程通知
            print("极光推送：点击远程通知: \(userInfo)")

            // 处理通知点击事件
            handleNotificationClick(userInfo: userInfo)
        } else {
            // 本地通知
            print("极光推送：点击本地通知: \(userInfo)")
        }

        completionHandler()
    }

    // 处理通知点击事件 - 只存储数据给 Flutter 处理
    private func handleNotificationClick(userInfo: [AnyHashable: Any]) {
        print("极光推送：通知点击，存储数据给 Flutter 处理: \(userInfo)")

        // 将推送数据存储起来，等 Flutter 启动后再处理
        if let data = try? JSONSerialization.data(withJSONObject: userInfo, options: []),
           let jsonString = String(data: data, encoding: .utf8) {
            UserDefaults.standard.set(jsonString, forKey: "jpush_notification_data")
            UserDefaults.standard.synchronize()
        }
    }

}

