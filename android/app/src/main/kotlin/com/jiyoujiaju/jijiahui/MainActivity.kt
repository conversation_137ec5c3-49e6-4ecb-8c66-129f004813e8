package com.jiyoujiaju.jijiahui

import android.content.Intent
import android.os.Bundle
import android.util.Log
import cn.jpush.android.api.JPushInterface
import com.thingclips.smart.activator.plug.mesosphere.ThingDeviceActivatorManager
import com.thingclips.smart.bizbundle.initializer.BizBundleInitializer
import io.flutter.embedding.android.FlutterActivity
import io.flutter.embedding.engine.FlutterEngine
import io.flutter.plugin.common.MethodChannel

/**
 * MainActivity：初始化 Flutter 引擎并手动注册本地插件
 */
class MainActivity : FlutterActivity() {

    companion object {
        private const val TAG = "MainActivity"
    }

    private var navigationChannel: MethodChannel? = null

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)

        // 初始化极光推送
        JPushInterface.setDebugMode(true) // 设置开启日志，发布时请关闭日志
        JPushInterface.init(this) // 初始化 JPush

        Log.d(TAG, "JPush 初始化完成")
    }

    override fun configureFlutterEngine(flutterEngine: FlutterEngine) {
        super.configureFlutterEngine(flutterEngine)
        // 手动把你的 FlutterUserPlugin 注册进来
        flutterEngine
            .plugins
            .add(FlutterUserPlugin())

        // 把 factory 注册到 engine，让它知道 viewType 对应哪个原生组件
        flutterEngine
            .platformViewsController
            .registry
            .registerViewFactory(
                "native_android_smartlife",
                SmartHomeViewFactory()
            )

        // 配置导航方法通道
        setupNavigationChannel(flutterEngine)
    }

    private fun setupNavigationChannel(flutterEngine: FlutterEngine) {
        navigationChannel = MethodChannel(flutterEngine.dartExecutor.binaryMessenger, "com.smartlife.navigation")
        navigationChannel?.setMethodCallHandler { call, result ->
            when (call.method) {
                "showNavigationBar" -> {
                    // Android 端可以根据需要实现导航栏显示逻辑
                    result.success(null)
                }
                "hideNavigationBar" -> {
                    // Android 端可以根据需要实现导航栏隐藏逻辑
                    result.success(null)
                }
                "popToFlutter" -> {
                    // Android 端不需要特殊处理，Flutter 会自己处理页面返回
                    // 这里只是确认收到了消息，让 Flutter 端知道可以安全地执行 Navigator.pop()
                    result.success(null)
                }
                else -> result.notImplemented()
            }
        }
    }

    /**
     * 导航到 Flutter 路由
     */
    fun navigateToFlutterRoute(route: String) {
        navigationChannel?.invokeMethod(route, null)
    }

    override fun onResume() {
        super.onResume()
        // 每次 Activity 可见／从后台恢复时都会执行
        BizBundleInitializer.onLogin()

        // 极光推送统计
        JPushInterface.onResume(this)
    }

    override fun onPause() {
        super.onPause()
        // 极光推送统计
        JPushInterface.onPause(this)
    }

    override fun onDestroy() {
        super.onDestroy()
        Log.d(TAG, "MainActivity onDestroy")
    }

}