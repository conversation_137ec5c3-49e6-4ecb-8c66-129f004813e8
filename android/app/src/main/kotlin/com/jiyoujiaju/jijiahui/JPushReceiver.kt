package com.jiyoujiaju.jijiahui

import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import android.os.Bundle
import android.util.Log
import cn.jpush.android.api.JPushInterface
import org.json.JSONException
import org.json.JSONObject

/**
 * 极光推送自定义接收器
 */
class JPushReceiver : BroadcastReceiver() {

    companion object {
        private const val TAG = "JPushReceiver"
    }

    override fun onReceive(context: Context, intent: Intent) {
        try {
            val bundle = intent.extras
            Log.d(TAG, "[JPushReceiver] onReceive - ${intent.action}, extras: $bundle")

            when (intent.action) {
                JPushInterface.ACTION_REGISTRATION_ID -> {
                    val regId = bundle?.getString(JPushInterface.EXTRA_REGISTRATION_ID)
                    Log.d(TAG, "[JPushReceiver] 接收Registration Id : $regId")
                    // 可以将 Registration Id 发送给应用服务器
                }

                JPushInterface.ACTION_MESSAGE_RECEIVED -> {
                    Log.d(TAG, "[JPushReceiver] 接收到推送下来的自定义消息: ${bundle.toString()}")
                    processCustomMessage(context, bundle)
                }

                JPushInterface.ACTION_NOTIFICATION_RECEIVED -> {
                    Log.d(TAG, "[JPushReceiver] 接收到推送下来的通知")
                    val notificationId = bundle?.getInt(JPushInterface.EXTRA_NOTIFICATION_ID)
                    Log.d(TAG, "[JPushReceiver] 接收到推送下来的通知的ID: $notificationId")
                }

                JPushInterface.ACTION_NOTIFICATION_OPENED -> {
                    Log.d(TAG, "[JPushReceiver] 用户点击打开了通知")
                    // 在这里可以自定义处理用户点击通知后的行为
                    openNotification(context, bundle)
                }

                JPushInterface.ACTION_CONNECTION_CHANGE -> {
                    val connected = intent.getBooleanExtra(JPushInterface.EXTRA_CONNECTION_CHANGE, false)
                    Log.w(TAG, "[JPushReceiver] 连接状态变化 connected:$connected")
                }

                else -> {
                    Log.d(TAG, "[JPushReceiver] Unhandled intent - ${intent.action}")
                }
            }
        } catch (e: Exception) {
            Log.e(TAG, "JPushReceiver onReceive error", e)
        }
    }

    // 处理自定义消息
    private fun processCustomMessage(context: Context, bundle: Bundle?) {
        val title = bundle?.getString(JPushInterface.EXTRA_TITLE)
        val message = bundle?.getString(JPushInterface.EXTRA_MESSAGE)
        val extra = bundle?.getString(JPushInterface.EXTRA_EXTRA)

        Log.d(TAG, "自定义消息 - title: $title, message: $message, extra: $extra")
    }

    // 处理通知点击 - 只启动应用并传递数据给 Flutter
    private fun openNotification(context: Context, bundle: Bundle?) {
        val extra = bundle?.getString(JPushInterface.EXTRA_EXTRA)

        Log.d(TAG, "通知点击，启动应用并传递数据: $extra")

        // 启动主 Activity，并传递推送数据给 Flutter 处理
        val intent = Intent(context, MainActivity::class.java).apply {
            addFlags(Intent.FLAG_ACTIVITY_NEW_TASK or Intent.FLAG_ACTIVITY_CLEAR_TOP)
            putExtra("jpush_notification_data", extra)
        }
        context.startActivity(intent)
    }
}
