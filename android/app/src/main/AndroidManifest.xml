<manifest xmlns:android="http://schemas.android.com/apk/res/android" 
    xmlns:tools="http://schemas.android.com/tools"
    package="com.jiyoujiaju.jijiahui">
    <!-- 相机权限 -->
    <uses-feature
        android:name="android.hardware.camera"
        android:required="false" />
    <uses-permission android:name="android.permission.CAMERA"/>
    <!-- 相册读写权限 -->
    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE"/>
    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE"/>
    
    <!-- 微信支付相关权限 -->
    <uses-permission android:name="android.permission.INTERNET"/>
    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE"/>

    <!--    涂鸦所需权限-->
    <uses-permission android:name="android.permission.ACCESS_FINE_LOCATION"/>
    <uses-permission android:name="android.permission.ACCESS_WIFI_STATE"/>
    <uses-permission android:name="android.permission.CHANGE_WIFI_STATE"/>
    <!-- 蓝牙权限 -->
    <uses-permission android:name="android.permission.BLUETOOTH"/>
    <uses-permission android:name="android.permission.BLUETOOTH_ADMIN"/>
    <uses-permission android:name="android.permission.BLUETOOTH_SCAN"/>
    <uses-permission android:name="android.permission.BLUETOOTH_CONNECT"/>
    <uses-permission android:name="android.permission.ACCESS_COARSE_LOCATION"/>
    <!-- 推送所需：读取网络状态、振动、唤醒等 -->
    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE"/>
    <uses-permission android:name="android.permission.ACCESS_WIFI_STATE"/>
    <uses-permission android:name="android.permission.WAKE_LOCK"/>
    <uses-permission android:name="android.permission.VIBRATE"/>

    <!-- 麦克风权限 - 用于语音控制 -->
    <uses-permission android:name="android.permission.RECORD_AUDIO"/>

    <!-- 通讯录权限 - 用于控制智能设备 -->
    <uses-permission android:name="android.permission.READ_CONTACTS"/>

    <!-- 媒体库权限 - 用于控制智能设备 -->
    <uses-permission android:name="android.permission.READ_MEDIA_AUDIO"/>
    <uses-permission android:name="android.permission.READ_MEDIA_VIDEO"/>
    <uses-permission android:name="android.permission.READ_MEDIA_IMAGES"/>

    <!-- Android 13+ 新的媒体权限 -->
    <uses-permission android:name="android.permission.READ_MEDIA_VISUAL_USER_SELECTED"/>

    <!-- 通知权限 (Android 13+) -->
    <uses-permission android:name="android.permission.POST_NOTIFICATIONS"/>

    <!-- 前台服务权限 -->
    <uses-permission android:name="android.permission.FOREGROUND_SERVICE"/>

    <!-- 系统设置权限 -->
    <uses-permission android:name="android.permission.WRITE_SETTINGS"/>

    <!-- 安装应用权限 -->
    <uses-permission android:name="android.permission.REQUEST_INSTALL_PACKAGES"/>

    <!-- 查询所有包权限 (Android 11+) -->
    <uses-permission android:name="android.permission.QUERY_ALL_PACKAGES"/>

    <!-- 本地网络权限 (Android 14+) -->
    <uses-permission android:name="android.permission.NEARBY_WIFI_DEVICES"/>

    <!-- 蓝牙广播权限 (Android 12+) -->
    <uses-permission android:name="android.permission.BLUETOOTH_ADVERTISE"/>

    <!-- 精确闹钟权限 (Android 12+) -->
    <uses-permission android:name="android.permission.SCHEDULE_EXACT_ALARM"/>

    <!-- 后台位置权限 -->
    <uses-permission android:name="android.permission.ACCESS_BACKGROUND_LOCATION"/>
    <application
        android:label="极家汇"
        tools:replace="android:label"
        android:name=".ThingSmartApp"
        android:icon="@mipmap/ic_launcher"
        android:requestLegacyExternalStorage="true"
        android:preserveLegacyExternalStorage="true"
        android:usesCleartextTraffic="true">
        <meta-data
            android:name="com.tencent.mm.sdk.appid"
            android:value="wx8531759f373d8a56" /> 
        <meta-data
            android:name="THING_SMART_APPKEY"
            android:value="uf7ugr53x9syf9k9kdpj" />
            <meta-data
            android:name="THING_SMART_SECRET"
            android:value="uyv7xmxdm7ryxenjsysmukshkdchwqns" />

        <!-- 极光推送配置 -->
        <meta-data
            android:name="JPUSH_APPKEY"
            android:value="e4b1e2e32964eff17b239b67" />
        <meta-data
            android:name="JPUSH_CHANNEL"
            android:value="developer-default" />

        <activity
            android:name=".MainActivity"
            android:exported="true"
            android:launchMode="singleTop"
            android:taskAffinity=""
            android:theme="@style/LaunchTheme"
            android:configChanges="orientation|keyboardHidden|keyboard|screenSize|smallestScreenSize|locale|layoutDirection|fontScale|screenLayout|density|uiMode"
            android:hardwareAccelerated="true"
            android:windowSoftInputMode="adjustResize">
            <!-- Specifies an Android theme to apply to this Activity as soon as
                 the Android process has started. This theme is visible to the user
                 while the Flutter UI initializes. After that, this theme continues
                 to determine the Window background behind the Flutter UI. -->
            <meta-data
              android:name="io.flutter.embedding.android.NormalTheme"
              android:resource="@style/NormalTheme"
              />
            <intent-filter>
                <action android:name="android.intent.action.MAIN"/>
                <category android:name="android.intent.category.LAUNCHER"/>
            </intent-filter>
        </activity>

        <activity android:name=".MyHomeActivity" />
        <activity android:name=".SmartDeviceSegmentActivity" />
        <!-- Don't delete the meta-data below.
             This is used by the Flutter tool to generate GeneratedPluginRegistrant.java -->
        <meta-data
            android:name="flutterEmbedding"
            android:value="2" />
        <!-- 极光推送 Service -->
        <service
            android:name="cn.jpush.android.service.PushService"
            android:enabled="true"
            android:exported="false"/>

        <!-- 极光推送自定义消息接收器 -->
        <receiver
            android:name=".JPushReceiver"
            android:exported="false">
            <intent-filter>
                <action android:name="cn.jpush.android.intent.REGISTRATION"/>
                <action android:name="cn.jpush.android.intent.MESSAGE_RECEIVED"/>
                <action android:name="cn.jpush.android.intent.NOTIFICATION_RECEIVED"/>
                <action android:name="cn.jpush.android.intent.NOTIFICATION_OPENED"/>
                <action android:name="cn.jpush.android.intent.CONNECTION"/>
                <category android:name="${applicationId}"/>
            </intent-filter>
        </receiver>

        <!-- 连接状态 Receiver -->
        <receiver
            android:name="cn.jpush.android.service.NetworkStateReceiver"
            android:exported="false">
            <intent-filter>
                <action android:name="android.net.conn.CONNECTIVITY_CHANGE"/>
                <action android:name="android.net.wifi.WIFI_STATE_CHANGED"/>
            </intent-filter>
        </receiver>
        <!-- 启动 Receiver -->
        <receiver
            android:name="cn.jpush.android.service.AlarmReceiver"
            android:exported="false"/>

    </application>
    <!-- Required to query activities that can process text, see:
         https://developer.android.com/training/package-visibility and
         https://developer.android.com/reference/android/content/Intent#ACTION_PROCESS_TEXT.

         In particular, this is used by the Flutter engine in io.flutter.plugin.text.ProcessTextPlugin. -->
    <queries>
        <intent>
            <action android:name="android.intent.action.PROCESS_TEXT"/>
            <data android:mimeType="text/plain"/>
        </intent>
        <!-- 微信相关查询 -->
        <package android:name="com.tencent.mm"/>
        <!-- 相机应用查询 -->
        <intent>
            <action android:name="android.media.action.IMAGE_CAPTURE"/>
        </intent>
        <!-- 相册应用查询 -->
        <intent>
            <action android:name="android.intent.action.PICK"/>
            <data android:mimeType="image/*"/>
        </intent>
        <!-- 文件管理器查询 -->
        <intent>
            <action android:name="android.intent.action.GET_CONTENT"/>
            <data android:mimeType="*/*"/>
        </intent>
        <!-- 浏览器查询 -->
        <intent>
            <action android:name="android.intent.action.VIEW"/>
            <data android:scheme="http"/>
        </intent>
        <intent>
            <action android:name="android.intent.action.VIEW"/>
            <data android:scheme="https"/>
        </intent>
    </queries>
</manifest>
