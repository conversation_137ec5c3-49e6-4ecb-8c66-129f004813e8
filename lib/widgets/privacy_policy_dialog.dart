import 'package:flutter/material.dart';
import 'package:flutter/gestures.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_smarthome/core/utils/custom_webview.dart';
import 'package:flutter_smarthome/core/utils/user_manager.dart';

class PrivacyPolicyDialog extends StatelessWidget {
  final VoidCallback? onAgree;
  final VoidCallback? onDisagree;

  const PrivacyPolicyDialog({
    Key? key,
    this.onAgree,
    this.onDisagree,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return PopScope(
      canPop: false, // 禁止返回键关闭弹窗
      child: Material(
        color: Colors.black54,
        child: Center(
          child: Container(
            margin: EdgeInsets.symmetric(horizontal: 40.w),
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(12.r),
            ),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                // 标题
                Container(
                  padding: EdgeInsets.only(top: 24.h, bottom: 16.h),
                  child: Text(
                    '服务协议和隐私政策',
                    style: TextStyle(
                      fontSize: 18.sp,
                      fontWeight: FontWeight.w600,
                      color: Colors.black,
                    ),
                  ),
                ),

                // 内容
                Container(
                  padding: EdgeInsets.symmetric(horizontal: 24.w),
                  child: Column(
                    children: [
                      Text(
                        '当前可使用极家汇应用服务，需要联网。上传个人头像等功能需要读取图片、文件。本应用需要联网使用读取并传送图片、视频、文件、MAC 地址、已安装应用列表以提供应用下载服务。部分智能设备需要用到定位权限，我们仅在您使用具体业务功能时，才会触发上述行为收集使用相关的个人信息。',
                        style: TextStyle(
                          fontSize: 14.sp,
                          color: const Color(0xFF333333),
                          height: 1.5,
                        ),
                      ),
                      SizedBox(height: 16.h),
                      RichText(
                        text: TextSpan(
                          style: TextStyle(
                            fontSize: 14.sp,
                            color: const Color(0xFF333333),
                            height: 1.5,
                          ),
                          children: [
                            const TextSpan(text: '您可阅读'),
                            TextSpan(
                              text: '《用户协议》',
                              style: TextStyle(
                                color: const Color(0xFF007AFF),
                                decoration: TextDecoration.underline,
                              ),
                              recognizer: TapGestureRecognizer()
                                ..onTap = () => _openUserAgreement(context),
                            ),
                            const TextSpan(text: '和'),
                            TextSpan(
                              text: '《隐私政策》',
                              style: TextStyle(
                                color: const Color(0xFF007AFF),
                                decoration: TextDecoration.underline,
                              ),
                              recognizer: TapGestureRecognizer()
                                ..onTap = () => _openPrivacyPolicy(context),
                            ),
                            const TextSpan(
                                text: '了解详细信息。如果您同意，请点击下面按钮开始接受我们的服务。'),
                          ],
                        ),
                      ),
                    ],
                  ),
                ),

                SizedBox(height: 32.h),

                // 按钮
                Container(
                  padding: EdgeInsets.symmetric(horizontal: 24.w),
                  child: Column(
                    children: [
                      // 同意按钮
                      SizedBox(
                        width: double.infinity,
                        height: 44.h,
                        child: ElevatedButton(
                          onPressed: () async {
                            await UserManager.instance.setPrivacyPolicyAgreed();
                            if (context.mounted) {
                              Navigator.of(context).pop();
                            }
                            onAgree?.call();
                          },
                          style: ElevatedButton.styleFrom(
                            backgroundColor: const Color(0xFF007AFF),
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(8.r),
                            ),
                            elevation: 0,
                          ),
                          child: Text(
                            '同意并接受',
                            style: TextStyle(
                              fontSize: 16.sp,
                              fontWeight: FontWeight.w500,
                              color: Colors.white,
                            ),
                          ),
                        ),
                      ),

                      SizedBox(height: 12.h),

                      // 不同意按钮
                      SizedBox(
                        width: double.infinity,
                        height: 44.h,
                        child: TextButton(
                          onPressed: () {
                            Navigator.of(context).pop();
                            onDisagree?.call();
                          },
                          style: TextButton.styleFrom(
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(8.r),
                              side: const BorderSide(
                                color: Color(0xFFDDDDDD),
                                width: 1,
                              ),
                            ),
                          ),
                          child: Text(
                            '暂不同意',
                            style: TextStyle(
                              fontSize: 16.sp,
                              fontWeight: FontWeight.w500,
                              color: const Color(0xFF666666),
                            ),
                          ),
                        ),
                      ),
                    ],
                  ),
                ),

                SizedBox(height: 24.h),
              ],
            ),
          ),
        ),
      ),
    );
  }

  void _openUserAgreement(BuildContext context) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => const CustomWebView(
          url: 'http://www.gazolife.com/app/html_JijiaAgreement.html',
          title: '用户协议',
        ),
      ),
    );
  }

  void _openPrivacyPolicy(BuildContext context) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => const CustomWebView(
          url: 'http://www.gazolife.com/app/jijia_PrivacyPolicy.html',
          title: '隐私政策',
        ),
      ),
    );
  }

  // 静态方法显示弹窗
  static Future<void> show(
    BuildContext context, {
    VoidCallback? onAgree,
    VoidCallback? onDisagree,
  }) {
    return showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => PrivacyPolicyDialog(
        onAgree: onAgree,
        onDisagree: onDisagree,
      ),
    );
  }
}
