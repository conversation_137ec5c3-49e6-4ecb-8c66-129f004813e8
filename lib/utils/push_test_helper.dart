import 'dart:convert';
import 'package:dio/dio.dart';
import 'package:flutter/foundation.dart';

/// 极光推送测试工具类
/// 用于发送测试推送消息
class PushTestHelper {
  static const String _baseUrl = 'https://api.jpush.cn/v3/push';
  static const String _appKey = 'e4b1e2e32964eff17b239b67';
  static const String _masterSecret = '4e16a5e62b65ba99177ffff2';
  
  static final Dio _dio = Dio();

  /// 发送推送给指定的 Registration ID
  static Future<bool> sendPushToRegistrationId({
    required String registrationId,
    required String title,
    required String content,
    Map<String, dynamic>? extras,
  }) async {
    try {
      final auth = base64Encode(utf8.encode('$_appKey:$_masterSecret'));
      
      final data = {
        'platform': ['android', 'ios'],
        'audience': {
          'registration_id': [registrationId]
        },
        'notification': {
          'alert': content,
          'android': {
            'title': title,
            'alert': content,
            'extras': extras ?? {},
          },
          'ios': {
            'alert': content,
            'badge': 1,
            'sound': 'default',
            'extras': extras ?? {},
          }
        },
        'message': {
          'msg_content': content,
          'title': title,
          'extras': extras ?? {},
        },
        'options': {
          'time_to_live': 86400,
          'apns_production': false, // 开发环境设为false
        }
      };

      final response = await _dio.post(
        _baseUrl,
        data: jsonEncode(data),
        options: Options(
          headers: {
            'Authorization': 'Basic $auth',
            'Content-Type': 'application/json',
          },
        ),
      );

      if (response.statusCode == 200) {
        debugPrint('推送发送成功: ${response.data}');
        return true;
      } else {
        debugPrint('推送发送失败: ${response.statusCode} - ${response.data}');
        return false;
      }
    } catch (e) {
      debugPrint('推送发送异常: $e');
      return false;
    }
  }

  /// 发送推送给指定别名
  static Future<bool> sendPushToAlias({
    required String alias,
    required String title,
    required String content,
    Map<String, dynamic>? extras,
  }) async {
    try {
      final auth = base64Encode(utf8.encode('$_appKey:$_masterSecret'));
      
      final data = {
        'platform': ['android', 'ios'],
        'audience': {
          'alias': [alias]
        },
        'notification': {
          'alert': content,
          'android': {
            'title': title,
            'alert': content,
            'extras': extras ?? {},
          },
          'ios': {
            'alert': content,
            'badge': 1,
            'sound': 'default',
            'extras': extras ?? {},
          }
        },
        'message': {
          'msg_content': content,
          'title': title,
          'extras': extras ?? {},
        },
        'options': {
          'time_to_live': 86400,
          'apns_production': false, // 开发环境设为false
        }
      };

      final response = await _dio.post(
        _baseUrl,
        data: jsonEncode(data),
        options: Options(
          headers: {
            'Authorization': 'Basic $auth',
            'Content-Type': 'application/json',
          },
        ),
      );

      if (response.statusCode == 200) {
        debugPrint('推送发送成功: ${response.data}');
        return true;
      } else {
        debugPrint('推送发送失败: ${response.statusCode} - ${response.data}');
        return false;
      }
    } catch (e) {
      debugPrint('推送发送异常: $e');
      return false;
    }
  }

  /// 发送推送给指定标签
  static Future<bool> sendPushToTags({
    required List<String> tags,
    required String title,
    required String content,
    Map<String, dynamic>? extras,
  }) async {
    try {
      final auth = base64Encode(utf8.encode('$_appKey:$_masterSecret'));
      
      final data = {
        'platform': ['android', 'ios'],
        'audience': {
          'tag': tags
        },
        'notification': {
          'alert': content,
          'android': {
            'title': title,
            'alert': content,
            'extras': extras ?? {},
          },
          'ios': {
            'alert': content,
            'badge': 1,
            'sound': 'default',
            'extras': extras ?? {},
          }
        },
        'message': {
          'msg_content': content,
          'title': title,
          'extras': extras ?? {},
        },
        'options': {
          'time_to_live': 86400,
          'apns_production': false, // 开发环境设为false
        }
      };

      final response = await _dio.post(
        _baseUrl,
        data: jsonEncode(data),
        options: Options(
          headers: {
            'Authorization': 'Basic $auth',
            'Content-Type': 'application/json',
          },
        ),
      );

      if (response.statusCode == 200) {
        debugPrint('推送发送成功: ${response.data}');
        return true;
      } else {
        debugPrint('推送发送失败: ${response.statusCode} - ${response.data}');
        return false;
      }
    } catch (e) {
      debugPrint('推送发送异常: $e');
      return false;
    }
  }

  /// 发送广播推送
  static Future<bool> sendBroadcastPush({
    required String title,
    required String content,
    Map<String, dynamic>? extras,
  }) async {
    try {
      final auth = base64Encode(utf8.encode('$_appKey:$_masterSecret'));
      
      final data = {
        'platform': ['android', 'ios'],
        'audience': 'all',
        'notification': {
          'alert': content,
          'android': {
            'title': title,
            'alert': content,
            'extras': extras ?? {},
          },
          'ios': {
            'alert': content,
            'badge': 1,
            'sound': 'default',
            'extras': extras ?? {},
          }
        },
        'message': {
          'msg_content': content,
          'title': title,
          'extras': extras ?? {},
        },
        'options': {
          'time_to_live': 86400,
          'apns_production': false, // 开发环境设为false
        }
      };

      final response = await _dio.post(
        _baseUrl,
        data: jsonEncode(data),
        options: Options(
          headers: {
            'Authorization': 'Basic $auth',
            'Content-Type': 'application/json',
          },
        ),
      );

      if (response.statusCode == 200) {
        debugPrint('推送发送成功: ${response.data}');
        return true;
      } else {
        debugPrint('推送发送失败: ${response.statusCode} - ${response.data}');
        return false;
      }
    } catch (e) {
      debugPrint('推送发送异常: $e');
      return false;
    }
  }

  /// 创建设备相关的推送附加数据
  static Map<String, dynamic> createDeviceExtras({
    required String deviceId,
    String? deviceName,
  }) {
    return {
      'type': 'device',
      'deviceId': deviceId,
      'deviceName': deviceName ?? '',
    };
  }

  /// 创建首页跳转的推送附加数据
  static Map<String, dynamic> createHomeExtras() {
    return {
      'type': 'home',
    };
  }
}
