import 'package:flutter/material.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_smarthome/core/models/models.dart';
import 'package:flutter_smarthome/core/network/api_manager.dart';
import 'package:flutter_smarthome/core/utils/hex_color.dart';
import 'package:flutter_smarthome/features/signature/esign_webview.dart';
import 'package:flutter_smarthome/shared/dialogs/discount_info_dialog.dart';

class ContractDetailWidget extends StatefulWidget {
  final Map<String, dynamic>? contractData;
  final String? esignContractId;
  final Future<void> Function()? onContractUpdated;
  const ContractDetailWidget(
      {super.key,
      required this.contractData,
      required this.esignContractId,
      this.onContractUpdated});

  @override
  State<ContractDetailWidget> createState() => _ContractDetailWidgetState();
}

class _ContractDetailWidgetState extends State<ContractDetailWidget> {
  // 表单数据
  String contractType = '';
  String paymentCompany = '';
  double contractAmount = 0.0;
  String decorationType = '';
  String startDate = '';
  String endDate = '';

  // 付款比例
  double firstPeriodPercentage = 0.0;
  double secondPeriodPercentage = 0.0;
  double thirdPeriodPercentage = 0.0;
  double fourthPeriodPercentage = 0.0;

  // 付款比例类型
  String paymentType = 'pay65'; // pay65, pay90, paySelf

  // 优惠相关状态
  bool _isDiscountExpanded = false;
  double discountAmount = 0.0; // 优惠金额
  List<String> discountDetails = []; // 优惠内容详情

  // 控制器
  final TextEditingController _contractAmountController =
      TextEditingController();
  final TextEditingController _firstPeriodController = TextEditingController();
  final TextEditingController _secondPeriodController = TextEditingController();
  final TextEditingController _thirdPeriodController = TextEditingController();
  final TextEditingController _fourthPeriodController = TextEditingController();

  // 收款公司选项
  List<Map<String, dynamic>> companyOptions = [];

  // 装修类型选项
  List<DictOption> _decorationTypeList = [];

  @override
  void initState() {
    super.initState();

    _initData();

    // 在初始化数据后再添加监听器
    _contractAmountController.addListener(() {
      setState(() {
        // 只有当输入框有内容时才更新contractAmount
        if (_contractAmountController.text.isNotEmpty) {
          contractAmount =
              double.tryParse(_contractAmountController.text) ?? 0.0;
        } else {
          contractAmount = 0.0;
        }
      });
    });

    _fetchPaymentCompanies();
    _fetchHouseType('pbs_decoration_type');
    _getDiscountInfo(widget.contractData?['customerProjectServiceId'] ?? '',
        widget.contractData?['contractType']);
  }

  // 强制隐藏键盘的辅助方法
  Future<void> _hideKeyboard() async {
    // 使用多种方法确保键盘被隐藏
    FocusScope.of(context).unfocus();
    FocusManager.instance.primaryFocus?.unfocus();

    // 清除所有文本字段的组合状态
    _contractAmountController.clearComposing();
  }

  //从contractData中获取数据给表单赋默认值
  void _initData() {
    if (widget.contractData != null) {
      setState(() {
        contractType = (widget.contractData?['contractType'] ?? '1') == "1"
            ? '一期合同'
            : '二期合同';
        paymentCompany = widget.contractData?['incomeCompanyName'] ?? '';
        // 获取原始价格，如果为null或0则不设置contractAmount
        double? originalPrice =
            widget.contractData?['originalPrice']?.toDouble();
        contractAmount =
            (originalPrice != null && originalPrice > 0) ? originalPrice : 0.0;

        String rawDecorationType =
            widget.contractData?['packageGroupTypeDisPlay'] ?? '';
        // 如果值是"未知"，则转换为空字符串，让界面显示"请选择"
        decorationType = (rawDecorationType == '未知') ? '' : rawDecorationType;

        // 调试：打印最终的 decorationType 值
        print('DEBUG: decorationType 最终值: "$decorationType"');

        startDate = widget.contractData?['contractConstructionStartDate'] ?? '';
        endDate = widget.contractData?['contractConstructionEndDate'] ?? '';

        // 处理付款类型
        paymentType = widget.contractData?['payType'] ?? 'pay65';

        // 处理付款比例数据
        List? paymentList = widget.contractData?['contractPaymentList'];
        if (paymentList != null && paymentList.isNotEmpty) {
          firstPeriodPercentage = (paymentList.isNotEmpty
                  ? (paymentList[0]['proportion'] ?? 0.0) * 100
                  : 0.0)
              .toDouble();
          secondPeriodPercentage = (paymentList.length > 1
                  ? (paymentList[1]['proportion'] ?? 0.0) * 100
                  : 0.0)
              .toDouble();
          thirdPeriodPercentage = (paymentList.length > 2
                  ? (paymentList[2]['proportion'] ?? 0.0) * 100
                  : 0.0)
              .toDouble();
          fourthPeriodPercentage = (paymentList.length > 3
                  ? (paymentList[3]['proportion'] ?? 0.0) * 100
                  : 0.0)
              .toDouble();
        } else {
          // 如果没有付款比例数据，根据paymentType设置默认值
          _setDefaultPaymentPercentages();
        }

        // 更新控制器的值
        // 如果合同金额为0或无效，则输入框为空
        if (contractAmount > 0) {
          _contractAmountController.text = contractAmount.toStringAsFixed(0);
        } else {
          _contractAmountController.text = '';
        }
        _firstPeriodController.text = firstPeriodPercentage.toStringAsFixed(0);
        _secondPeriodController.text =
            secondPeriodPercentage.toStringAsFixed(0);
        _thirdPeriodController.text = thirdPeriodPercentage.toStringAsFixed(0);
        _fourthPeriodController.text =
            fourthPeriodPercentage.toStringAsFixed(0);
      });
    } else {
      // 如果没有合同数据，确保输入框为空并设置默认付款比例
      _contractAmountController.text = '';
      _setDefaultPaymentPercentages();
    }
  }

  @override
  void dispose() {
    _contractAmountController.dispose();
    _firstPeriodController.dispose();
    _secondPeriodController.dispose();
    _thirdPeriodController.dispose();
    _fourthPeriodController.dispose();

    super.dispose();
  }

  // 计算总百分比
  double get totalPercentage =>
      firstPeriodPercentage +
      secondPeriodPercentage +
      thirdPeriodPercentage +
      fourthPeriodPercentage;

  // 表单验证
  bool get isFormValid {
    // 检查必填项
    if (contractType.isEmpty) return false;
    if (paymentCompany.isEmpty) return false;
    if (contractAmount <= 0) return false;
    if (decorationType.isEmpty) return false;
    if (startDate.isEmpty) return false;
    if (endDate.isEmpty) return false;

    // 检查比例总和是否为100%
    if (totalPercentage != 100.0) return false;

    return true;
  }

  // 构建付款类型选择按钮
  Widget _buildPaymentTypeButton(String label, String type,
      {bool isCompact = false}) {
    bool isSelected = paymentType == type;
    return isCompact
        ? GestureDetector(
            onTap: () => _updatePaymentType(type),
            child: Container(
              height: 28.h,
              padding: EdgeInsets.symmetric(horizontal: 12.w),
              decoration: BoxDecoration(
                color: isSelected
                    ? HexColor('#FFB26D')
                    : HexColor('#FFF4E0'), // 未选中时使用更浅的橙色背景
                borderRadius: BorderRadius.circular(14.r),
                border: Border.all(
                  color: isSelected
                      ? HexColor('#FFB26D')
                      : HexColor('#FFE0B3'), // 未选中时使用浅橙色边框
                  width: 1,
                ),
              ),
              child: Center(
                child: Text(
                  label,
                  style: TextStyle(
                    fontSize: 11.sp,
                    color: isSelected
                        ? Colors.white
                        : HexColor('#FFB26D'), // 未选中时使用橙色文字
                    fontWeight:
                        isSelected ? FontWeight.bold : FontWeight.normal,
                  ),
                ),
              ),
            ),
          )
        : Expanded(
            child: GestureDetector(
              onTap: () => _updatePaymentType(type),
              child: Container(
                height: 32.h,
                decoration: BoxDecoration(
                  color: isSelected
                      ? HexColor('#FFB26D')
                      : HexColor('#FFF4E0'), // 未选中时使用更浅的橙色背景
                  borderRadius: BorderRadius.circular(16.r),
                  border: Border.all(
                    color: isSelected
                        ? HexColor('#FFB26D')
                        : HexColor('#FFE0B3'), // 未选中时使用浅橙色边框
                    width: 1,
                  ),
                ),
                child: Center(
                  child: Text(
                    label,
                    style: TextStyle(
                      fontSize: 12.sp,
                      color: isSelected
                          ? Colors.white
                          : HexColor('#FFB26D'), // 未选中时使用橙色文字
                      fontWeight:
                          isSelected ? FontWeight.bold : FontWeight.normal,
                    ),
                  ),
                ),
              ),
            ),
          );
  }

  // 构建付款期数项
  Widget _buildPaymentItem(String period, double percentage,
      {bool isTotal = false}) {
    // 计算实际合同金额（原金额减去优惠金额）
    double actualContractAmount = contractAmount - discountAmount;
    double amount = actualContractAmount * (percentage / 100);

    // 根据期数获取对应的控制器
    TextEditingController? controller;
    switch (period) {
      case '一期':
        controller = _firstPeriodController;
        break;
      case '二期':
        controller = _secondPeriodController;
        break;
      case '三期':
        controller = _thirdPeriodController;
        break;
      case '四期':
        controller = _fourthPeriodController;
        break;
    }

    return Container(
      height: 56.h, // 固定高度，与表单项保持一致
      padding: EdgeInsets.symmetric(horizontal: 0.w),
      decoration: BoxDecoration(
        border: isTotal
            ? null
            : Border(
                bottom: BorderSide(
                  color: Colors.grey[200]!,
                  width: 1.h,
                ),
              ),
      ),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.center, // 确保内容垂直居中对齐
        children: [
          // 期数
          SizedBox(
            width: 60.w,
            child: Text(
              period,
              style: TextStyle(
                fontSize: 14.sp,
                color: Colors.black,
                fontWeight: FontWeight.w500,
              ),
            ),
          ),

          // 金额
          Expanded(
            child: Text(
              '¥${amount.toStringAsFixed(0)}',
              style: TextStyle(
                fontSize: 14.sp,
                color: Colors.grey[700],
                fontWeight: FontWeight.w400,
              ),
            ),
          ),

          // 比例输入
          if (!isTotal && controller != null)
            SizedBox(
              width: 80.w,
              height: 32.h,
              child: Row(
                children: [
                  Expanded(
                    child: TextField(
                      controller: controller,
                      keyboardType: TextInputType.number,
                      textAlign: TextAlign.center,
                      style: TextStyle(
                        fontSize: 14.sp,
                        color: HexColor('#FFB26D'),
                        fontWeight: FontWeight.bold,
                      ),
                      decoration: InputDecoration(
                        border: InputBorder.none,
                        contentPadding: EdgeInsets.zero,
                        isDense: true,
                        hintText: '请输入',
                        hintStyle: TextStyle(
                          fontSize: 14.sp,
                          color: Colors.grey[400],
                          fontWeight: FontWeight.normal,
                        ),
                      ),
                      onChanged: (value) {
                        double newPercentage = double.tryParse(value) ?? 0.0;
                        _updatePercentage(period, newPercentage);
                      },
                    ),
                  ),
                  Padding(
                    padding: EdgeInsets.only(right: 8.w),
                    child: Text(
                      '%',
                      style: TextStyle(
                        fontSize: 14.sp,
                        color: HexColor('#FFB26D'),
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                ],
              ),
            )
          else
            Text(
              '${percentage.toStringAsFixed(0)}%',
              style: TextStyle(
                fontSize: 14.sp,
                color: HexColor('#FFB26D'),
                fontWeight: FontWeight.bold,
              ),
            ),
        ],
      ),
    );
  }

  // 设置默认付款比例
  void _setDefaultPaymentPercentages() {
    switch (paymentType) {
      case 'pay65':
        firstPeriodPercentage = 65.0;
        secondPeriodPercentage = 0.0;
        thirdPeriodPercentage = 30.0;
        fourthPeriodPercentage = 5.0;
        break;
      case 'pay90':
        firstPeriodPercentage = 90.0;
        secondPeriodPercentage = 0.0;
        thirdPeriodPercentage = 5.0;
        fourthPeriodPercentage = 5.0;
        break;
      case 'paySelf':
        // 自定义比例保持当前值或默认为0
        break;
    }

    // 更新控制器显示
    _firstPeriodController.text = firstPeriodPercentage.toStringAsFixed(0);
    _secondPeriodController.text = secondPeriodPercentage.toStringAsFixed(0);
    _thirdPeriodController.text = thirdPeriodPercentage.toStringAsFixed(0);
    _fourthPeriodController.text = fourthPeriodPercentage.toStringAsFixed(0);
  }

  // 更新付款类型并设置对应比例
  void _updatePaymentType(String newType) {
    setState(() {
      paymentType = newType;
      _setDefaultPaymentPercentages();
    });
  }

  // 更新比例并保证联动
  void _updatePercentage(String period, double newPercentage) {
    setState(() {
      // 当用户手动修改比例时，自动切换到自定义模式
      paymentType = 'paySelf';
      // 获取其他三期的总和
      double othersTotal = 0.0;
      switch (period) {
        case '一期':
          othersTotal = secondPeriodPercentage +
              thirdPeriodPercentage +
              fourthPeriodPercentage;
          break;
        case '二期':
          othersTotal = firstPeriodPercentage +
              thirdPeriodPercentage +
              fourthPeriodPercentage;
          break;
        case '三期':
          othersTotal = firstPeriodPercentage +
              secondPeriodPercentage +
              fourthPeriodPercentage;
          break;
        case '四期':
          othersTotal = firstPeriodPercentage +
              secondPeriodPercentage +
              thirdPeriodPercentage;
          break;
      }

      // 限制新百分比不能超过剩余可用的百分比
      double maxAllowed = 100.0 - othersTotal;
      if (newPercentage > maxAllowed) {
        newPercentage = maxAllowed;
      }
      if (newPercentage < 0) {
        newPercentage = 0;
      }

      // 更新对应期数的百分比和控制器
      switch (period) {
        case '一期':
          firstPeriodPercentage = newPercentage;
          // 如果值被限制了，更新控制器显示正确的值
          if (_firstPeriodController.text != newPercentage.toStringAsFixed(0)) {
            _firstPeriodController.text = newPercentage.toStringAsFixed(0);
            _firstPeriodController.selection = TextSelection.fromPosition(
              TextPosition(offset: _firstPeriodController.text.length),
            );
          }
          break;
        case '二期':
          secondPeriodPercentage = newPercentage;
          if (_secondPeriodController.text !=
              newPercentage.toStringAsFixed(0)) {
            _secondPeriodController.text = newPercentage.toStringAsFixed(0);
            _secondPeriodController.selection = TextSelection.fromPosition(
              TextPosition(offset: _secondPeriodController.text.length),
            );
          }
          break;
        case '三期':
          thirdPeriodPercentage = newPercentage;
          if (_thirdPeriodController.text != newPercentage.toStringAsFixed(0)) {
            _thirdPeriodController.text = newPercentage.toStringAsFixed(0);
            _thirdPeriodController.selection = TextSelection.fromPosition(
              TextPosition(offset: _thirdPeriodController.text.length),
            );
          }
          break;
        case '四期':
          fourthPeriodPercentage = newPercentage;
          if (_fourthPeriodController.text !=
              newPercentage.toStringAsFixed(0)) {
            _fourthPeriodController.text = newPercentage.toStringAsFixed(0);
            _fourthPeriodController.selection = TextSelection.fromPosition(
              TextPosition(offset: _fourthPeriodController.text.length),
            );
          }
          break;
      }
    });
  }

  // 判断是否有优惠信息
  bool get hasDiscountInfo => discountAmount > 0 || discountDetails.isNotEmpty;

  // 构建优惠金额组件
  Widget _buildDiscountSection() {
    return Column(
      children: [
        // 第一行：优惠内容标题和申请优惠按钮（始终显示）
        Container(
          height: 56.h,
          padding: EdgeInsets.symmetric(horizontal: 0.w),
          decoration: BoxDecoration(
            border: Border(
              bottom: BorderSide(
                color: hasDiscountInfo && !_isDiscountExpanded
                    ? Colors.transparent // 折叠时隐藏分割线
                    : Colors.grey[200]!,
                width: 1.h,
              ),
            ),
          ),
          child: Row(
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              // 左侧标题
              Text(
                '优惠内容',
                style: TextStyle(
                  fontSize: 14.sp,
                  color: Colors.black,
                  fontWeight: FontWeight.w500,
                ),
              ),

              const Spacer(),

              // 右侧内容 - 申请优惠按钮和展开/折叠按钮
              Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  // 申请优惠按钮
                  GestureDetector(
                    onTap: () {
                      DiscountInfoDialog.show(
                        widget.contractData?['customerProjectServiceId'] ?? '',
                        contractType == '一期合同' ? '1' : '2',
                        context,
                        originalPrice: _getOriginalPrice(),
                        onClose: () {
                          // 对话框关闭时刷新合同数据
                          _refreshContractData();
                        },
                      );
                    },
                    child: Container(
                      height: 32.h,
                      padding: EdgeInsets.symmetric(horizontal: 12.w),
                      decoration: BoxDecoration(
                        gradient: const LinearGradient(
                          begin: Alignment.centerLeft,
                          end: Alignment.centerRight,
                          colors: [
                            Color(0xFFFFC276), // #FFC276
                            Color(0xFFFFAF2A), // #FFAF2A
                          ],
                        ),
                        borderRadius: BorderRadius.circular(16.r),
                      ),
                      child: Center(
                        child: Text(
                          '申请优惠',
                          style: TextStyle(
                            fontSize: 11.sp,
                            color: Colors.white,
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                      ),
                    ),
                  ),

                  // 展开/折叠按钮（只有当有优惠信息时才显示）
                  if (hasDiscountInfo) ...[
                    SizedBox(width: 8.w),
                    GestureDetector(
                      onTap: () {
                        setState(() {
                          _isDiscountExpanded = !_isDiscountExpanded;
                        });
                      },
                      child: Icon(
                        _isDiscountExpanded
                            ? Icons.keyboard_arrow_up
                            : Icons.keyboard_arrow_down,
                        color: Colors.grey[400],
                        size: 20.w,
                      ),
                    ),
                  ],
                ],
              ),
            ],
          ),
        ),

        // 第二行：优惠摘要信息（只有当有优惠信息且未展开时才显示，右对齐，紧凑布局）
        if (hasDiscountInfo && !_isDiscountExpanded)
          Container(
            height: 32.h, // 减少高度，更紧凑
            padding: EdgeInsets.symmetric(horizontal: 0.w),
            decoration: BoxDecoration(
              border: Border(
                bottom: BorderSide(
                  color: Colors.grey[200]!,
                  width: 1.h,
                ),
              ),
            ),
            child: Row(
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                const Spacer(),
                // 优惠摘要信息（右对齐）
                Text(
                  '优惠金额/内容：${discountAmount.toStringAsFixed(0)}元/${discountDetails.length}条',
                  style: TextStyle(
                    fontSize: 12.sp, // 稍微减小字体
                    color: Colors.grey[600],
                    fontWeight: FontWeight.w400,
                  ),
                ),
              ],
            ),
          ),
      ],
    );
  }

  // 构建优惠详情展开内容
  Widget _buildDiscountDetails() {
    // 如果没有优惠信息或未展开，返回空组件
    if (!hasDiscountInfo || !_isDiscountExpanded) {
      return const SizedBox.shrink();
    }

    return Column(
      children: [
        // 优惠金额行
        Container(
          height: 56.h,
          padding: EdgeInsets.symmetric(horizontal: 0.w),
          decoration: BoxDecoration(
            border: Border(
              bottom: BorderSide(
                color: Colors.grey[200]!,
                width: 1.h,
              ),
            ),
          ),
          child: Row(
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              Text(
                '优惠金额',
                style: TextStyle(
                  fontSize: 14.sp,
                  color: HexColor('#666666'),
                  fontWeight: FontWeight.w500,
                ),
              ),
              const Spacer(),
              Text(
                '${discountAmount.toStringAsFixed(0)}元',
                style: TextStyle(
                  fontSize: 14.sp,
                  color: HexColor('#666666'),
                  fontWeight: FontWeight.w400,
                ),
              ),
            ],
          ),
        ),

        // 优惠内容区域
        Padding(
          padding: EdgeInsets.symmetric(vertical: 8.h),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // 如果没有优惠详情，显示提示信息
              if (discountDetails.isEmpty)
                Container(
                  width: double.infinity,
                  padding:
                      EdgeInsets.symmetric(horizontal: 12.w, vertical: 16.h),
                  decoration: BoxDecoration(
                    color: HexColor('#F8F8F8'),
                    borderRadius: BorderRadius.circular(8.r),
                  ),
                  child: Text(
                    '暂无优惠内容',
                    textAlign: TextAlign.center,
                    style: TextStyle(
                      fontSize: 13.sp,
                      color: Colors.grey[500],
                      height: 1.4,
                    ),
                  ),
                )
              else
                // 优惠详情列表
                ...discountDetails.map((detail) {
                  int index = discountDetails.indexOf(detail);
                  return Padding(
                    padding: EdgeInsets.only(
                        bottom: index == discountDetails.length - 1 ? 0 : 12.h),
                    child: Row(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        // 序号圆圈 - 添加顶部边距使其与文本对齐
                        Padding(
                          padding: EdgeInsets.only(top: 8.h),
                          child: Container(
                            width: 20.w,
                            height: 20.w,
                            decoration: BoxDecoration(
                              color: HexColor('#FFB26D'),
                              shape: BoxShape.circle,
                            ),
                            child: Center(
                              child: Text(
                                '${index + 1}',
                                style: TextStyle(
                                  fontSize: 11.sp,
                                  color: Colors.white,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                            ),
                          ),
                        ),

                        SizedBox(width: 8.w),

                        // 详情文本（带灰色背景）
                        Expanded(
                          child: Container(
                            padding: EdgeInsets.symmetric(
                                horizontal: 12.w, vertical: 8.h),
                            decoration: BoxDecoration(
                              color: HexColor('#F8F8F8'),
                              borderRadius: BorderRadius.circular(8.r),
                            ),
                            child: Text(
                              detail,
                              style: TextStyle(
                                fontSize: 13.sp,
                                color: Colors.grey[600],
                                height: 1.4,
                              ),
                            ),
                          ),
                        ),
                      ],
                    ),
                  );
                }).toList(),
            ],
          ),
        ),
      ],
    );
  }

  // 构建表单项
  Widget _buildFormItem(String title, String value,
      {bool isSelectable = false,
      bool isEditable = false,
      TextEditingController? controller,
      VoidCallback? onTap}) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        height: 56.h, // 固定高度，确保所有表单项高度一致
        padding: EdgeInsets.symmetric(horizontal: 0.w),
        decoration: BoxDecoration(
          border: Border(
            bottom: BorderSide(
              color: Colors.grey[200]!,
              width: 1.h,
            ),
          ),
        ),
        child: Row(
          crossAxisAlignment: CrossAxisAlignment.center, // 确保内容垂直居中对齐
          children: [
            // 左侧标题（带红色星号）
            Row(
              children: [
                Text(
                  '*',
                  style: TextStyle(
                    color: Colors.red,
                    fontSize: 14.sp,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                SizedBox(width: 4.w),
                Text(
                  title,
                  style: TextStyle(
                    fontSize: 14.sp,
                    color: Colors.black,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ],
            ),

            const Spacer(),

            // 右侧值（右对齐）
            if (isEditable && controller != null)
              SizedBox(
                width: 120.w,
                child: TextField(
                  controller: controller,
                  keyboardType: TextInputType.number,
                  textAlign: TextAlign.right,
                  style: TextStyle(
                    fontSize: 14.sp,
                    color: Colors.grey[700],
                    fontWeight: FontWeight.w400,
                  ),
                  decoration: InputDecoration(
                    border: InputBorder.none,
                    contentPadding: EdgeInsets.zero,
                    suffixText: '元',
                    hintText: '请输入金额',
                    prefixStyle: TextStyle(
                      fontSize: 14.sp,
                      color: Colors.grey[700],
                    ),
                    hintStyle: TextStyle(
                      fontSize: 14.sp,
                      color: Colors.grey[400],
                    ),
                    isDense: true,
                  ),
                ),
              )
            else
              Row(
                mainAxisSize: MainAxisSize.min, // 确保Row不占用多余空间
                children: [
                  Text(
                    value.isEmpty ? '请选择' : value,
                    style: TextStyle(
                      fontSize: 14.sp,
                      color:
                          value.isEmpty ? Colors.grey[400] : Colors.grey[700],
                      fontWeight: FontWeight.w400,
                    ),
                  ),
                  if (isSelectable) ...[
                    SizedBox(width: 8.w),
                    Icon(
                      Icons.keyboard_arrow_right,
                      color: Colors.grey[400],
                      size: 20.w,
                    ),
                  ],
                ],
              ),
          ],
        ),
      ),
    );
  }

  // 显示日期选择器
  void _showDatePicker(String title, Function(String) onDateSelected) {
    // 解析当前日期，如果没有则使用今天
    DateTime selectedDate = DateTime.now();

    // 根据标题判断是开工日期还是完工日期，并尝试解析已有的日期
    String currentDateString = '';
    if (title.contains('开工')) {
      currentDateString = startDate;
    } else if (title.contains('完工')) {
      currentDateString = endDate;
    }

    // 如果已有日期，尝试解析
    if (currentDateString.isNotEmpty) {
      try {
        List<String> dateParts = currentDateString.split('-');
        if (dateParts.length == 3) {
          int year = int.parse(dateParts[0]);
          int month = int.parse(dateParts[1]);
          int day = int.parse(dateParts[2]);
          selectedDate = DateTime(year, month, day);
        }
      } catch (e) {
        // 解析失败，使用今天的日期
        selectedDate = DateTime.now();
      }
    }

    showModalBottomSheet(
      context: context,
      backgroundColor: Colors.transparent,
      builder: (context) => Container(
        height: 300.h,
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.vertical(top: Radius.circular(12.r)),
        ),
        child: Column(
          children: [
            // 拖拽指示器
            Container(
              width: 36.w,
              height: 4.h,
              margin: EdgeInsets.only(top: 8.h),
              decoration: BoxDecoration(
                color: Colors.grey[300],
                borderRadius: BorderRadius.circular(2.r),
              ),
            ),

            // 标题栏
            Container(
              height: 50.h,
              padding: EdgeInsets.symmetric(horizontal: 16.w),
              decoration: BoxDecoration(
                border: Border(
                  bottom: BorderSide(
                    color: Colors.grey[200]!,
                    width: 1.h,
                  ),
                ),
              ),
              child: Row(
                children: [
                  TextButton(
                    onPressed: () => Navigator.pop(context),
                    child: Text(
                      '取消',
                      style: TextStyle(
                        color: Colors.grey[600],
                        fontSize: 16.sp,
                      ),
                    ),
                  ),
                  Expanded(
                    child: Text(
                      title,
                      textAlign: TextAlign.center,
                      style: TextStyle(
                        fontSize: 16.sp,
                        fontWeight: FontWeight.bold,
                        color: Colors.black,
                      ),
                    ),
                  ),
                  TextButton(
                    onPressed: () {
                      Navigator.pop(context);
                      String formattedDate =
                          '${selectedDate.year}-${selectedDate.month.toString().padLeft(2, '0')}-${selectedDate.day.toString().padLeft(2, '0')}';
                      onDateSelected(formattedDate);
                    },
                    child: Text(
                      '确定',
                      style: TextStyle(
                        color: Colors.black,
                        fontSize: 16.sp,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                ],
              ),
            ),

            // 日期选择器
            Expanded(
              child: CupertinoDatePicker(
                mode: CupertinoDatePickerMode.date,
                initialDateTime: selectedDate,
                minimumDate: DateTime(2020),
                maximumDate: DateTime(2030),
                onDateTimeChanged: (DateTime newDate) {
                  selectedDate = newDate;
                },
              ),
            ),
          ],
        ),
      ),
    );
  }

  // 显示选择器底推框
  void _showPickerBottomSheet(String title, List<String> options,
      String currentValue, Function(String) onSelected) {
    int selectedIndex = options.indexOf(currentValue);
    if (selectedIndex == -1) selectedIndex = 0;

    showModalBottomSheet(
      context: context,
      backgroundColor: Colors.transparent,
      builder: (context) => Container(
        height: 300.h,
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.vertical(top: Radius.circular(12.r)),
        ),
        child: Column(
          children: [
            // 拖拽指示器
            Container(
              width: 36.w,
              height: 4.h,
              margin: EdgeInsets.only(top: 8.h),
              decoration: BoxDecoration(
                color: Colors.grey[300],
                borderRadius: BorderRadius.circular(2.r),
              ),
            ),

            // 标题栏
            Container(
              height: 50.h,
              padding: EdgeInsets.symmetric(horizontal: 16.w),
              decoration: BoxDecoration(
                border: Border(
                  bottom: BorderSide(
                    color: Colors.grey[200]!,
                    width: 1.h,
                  ),
                ),
              ),
              child: Row(
                children: [
                  TextButton(
                    onPressed: () => Navigator.pop(context),
                    child: Text(
                      '取消',
                      style: TextStyle(
                        color: Colors.grey[600],
                        fontSize: 16.sp,
                      ),
                    ),
                  ),
                  Expanded(
                    child: Text(
                      title,
                      textAlign: TextAlign.center,
                      style: TextStyle(
                        fontSize: 16.sp,
                        fontWeight: FontWeight.bold,
                        color: Colors.black,
                      ),
                    ),
                  ),
                  TextButton(
                    onPressed: () {
                      Navigator.pop(context);
                      onSelected(options[selectedIndex]);
                    },
                    child: Text(
                      '确定',
                      style: TextStyle(
                        color: Colors.black,
                        fontSize: 16.sp,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                ],
              ),
            ),

            // 选择器
            Expanded(
              child: CupertinoPicker(
                itemExtent: 44.h,
                scrollController:
                    FixedExtentScrollController(initialItem: selectedIndex),
                onSelectedItemChanged: (index) {
                  selectedIndex = index;
                },
                children: options
                    .map(
                      (option) => Center(
                        child: Text(
                          option,
                          style: TextStyle(
                            fontSize: 16.sp,
                            color: Colors.black,
                          ),
                        ),
                      ),
                    )
                    .toList(),
              ),
            ),
          ],
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () {
        // 点击空白处收起键盘
        FocusScope.of(context).unfocus();
      },
      child: Scaffold(
        extendBodyBehindAppBar: true,
        appBar: AppBar(
          title: const Text('裸签合同'),
          //标题加粗
          titleTextStyle: TextStyle(
            fontWeight: FontWeight.bold,
            fontSize: 18.sp,
            color: Colors.black,
          ),
          backgroundColor: Colors.transparent,
          foregroundColor: const Color.fromRGBO(0, 0, 0, 1),
          elevation: 0,
          scrolledUnderElevation: 0,
          surfaceTintColor: Colors.transparent,
        ),
        body: Container(
          decoration: const BoxDecoration(
            gradient: LinearGradient(
              begin: Alignment.topCenter,
              end: Alignment.bottomCenter,
              stops: [0.0, 0.8], // 渐变范围调整，延长到80%
              colors: [
                Color(0xFFFFF4E0), // #FFF4E0 顶部颜色
                Colors.white, // 白色 底部颜色
              ],
            ),
          ),
          child: SafeArea(
            child: SingleChildScrollView(
              padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 16.h),
              child: Column(
                children: [
                  // 表单信息卡片
                  Container(
                    decoration: BoxDecoration(
                      color: Colors.white,
                      borderRadius: BorderRadius.circular(12.r),
                      boxShadow: [
                        BoxShadow(
                          color: Colors.black.withOpacity(0.05),
                          blurRadius: 10,
                          offset: const Offset(0, 2),
                        ),
                      ],
                    ),
                    padding: EdgeInsets.fromLTRB(16.w, 0.h, 16.w, 12.h),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        _buildFormItem('合同类型', contractType),
                        _buildFormItem('收款公司', paymentCompany,
                            isSelectable: true, onTap: () async {
                          // 强制隐藏键盘
                          await _hideKeyboard();
                          // 确保键盘完全收起后再显示选择器
                          Future.delayed(const Duration(milliseconds: 200), () {
                            _showPickerBottomSheet(
                                '选择收款公司',
                                companyOptions
                                    .map(
                                        (option) => option['companyName'] ?? '')
                                    .toList()
                                    .cast<String>(),
                                paymentCompany, (selected) {
                              setState(() {
                                paymentCompany = selected;
                              });
                            });
                          });
                        }),
                        _buildFormItem('合同金额', '',
                            isEditable: true,
                            controller: _contractAmountController),
                        _buildFormItem('装修类型', decorationType,
                            isSelectable: true, onTap: () async {
                          // 强制隐藏键盘
                          await _hideKeyboard();
                          // 确保键盘完全收起后再显示选择器
                          Future.delayed(const Duration(milliseconds: 200), () {
                            _showPickerBottomSheet(
                                '选择装修类型',
                                _decorationTypeList
                                    .map((option) => option.label)
                                    .toList()
                                    .cast<String>(),
                                decorationType, (selected) {
                              setState(() {
                                decorationType = selected;
                              });
                            });
                          });
                        }),
                        _buildFormItem('合同开工日期', startDate, isSelectable: true,
                            onTap: () async {
                          // 强制隐藏键盘
                          await _hideKeyboard();
                          // 确保键盘完全收起后再显示选择器
                          Future.delayed(const Duration(milliseconds: 200), () {
                            _showDatePicker('选择开工日期', (selectedDate) {
                              setState(() {
                                startDate = selectedDate;
                              });
                            });
                          });
                        }),
                        _buildFormItem('合同完工日期', endDate, isSelectable: true,
                            onTap: () async {
                          // 强制隐藏键盘
                          await _hideKeyboard();
                          // 确保键盘完全收起后再显示选择器
                          Future.delayed(const Duration(milliseconds: 200), () {
                            _showDatePicker('选择完工日期', (selectedDate) {
                              setState(() {
                                endDate = selectedDate;
                              });
                            });
                          });
                        }),
                        // 优惠金额组件
                        _buildDiscountSection(),
                        // 优惠详情展开内容
                        _buildDiscountDetails(),
                      ],
                    ),
                  ),

                  // 间距
                  SizedBox(height: 20.h),
                  // 付款比例卡片
                  Container(
                    decoration: BoxDecoration(
                      color: Colors.white,
                      borderRadius: BorderRadius.circular(12.r),
                      boxShadow: [
                        BoxShadow(
                          color: Colors.black.withOpacity(0.05),
                          blurRadius: 10,
                          offset: const Offset(0, 2),
                        ),
                      ],
                    ),
                    padding:
                        EdgeInsets.symmetric(horizontal: 16.w, vertical: 12.h),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        // 标题和按钮在同一行
                        Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            Text(
                              '付款比例',
                              style: TextStyle(
                                fontSize: 14.sp,
                                fontWeight: FontWeight.bold,
                                color: Colors.black,
                              ),
                            ),
                            SizedBox(height: 16.h),
                            Text(
                              '首期款:',
                              style: TextStyle(
                                fontSize: 14.sp,
                                color: HexColor('#FFA555'),
                              ),
                            ),
                            Row(
                              children: [
                                _buildPaymentTypeButton('65%', 'pay65',
                                    isCompact: true),
                                SizedBox(width: 8.w),
                                _buildPaymentTypeButton('90%', 'pay90',
                                    isCompact: true),
                                SizedBox(width: 8.w),
                                _buildPaymentTypeButton('自定义', 'paySelf',
                                    isCompact: true),
                              ],
                            ),
                          ],
                        ),
                        SizedBox(height: 16.h),
                        _buildPaymentItem('一期', firstPeriodPercentage),
                        _buildPaymentItem('二期', secondPeriodPercentage),
                        _buildPaymentItem('三期', thirdPeriodPercentage),
                        _buildPaymentItem('四期', fourthPeriodPercentage),
                        _buildPaymentItem('合计', totalPercentage, isTotal: true),
                      ],
                    ),
                  ),

                  // 添加底部间距，避免与底部按钮重叠
                  SizedBox(height: 80.h),
                ],
              ),
            ),
          ),
        ),
        //高度 44 的提交按钮
        bottomNavigationBar: Container(
            color: Colors.white,
            padding: EdgeInsets.symmetric(horizontal: 16.w),
            child: SafeArea(
              child: ElevatedButton(
                onPressed: isFormValid ? _showSubmitConfirmDialog : null,
                style: ElevatedButton.styleFrom(
                  backgroundColor:
                      isFormValid ? Colors.black : Colors.grey[400],
                  foregroundColor: Colors.white,
                  minimumSize: Size(double.infinity, 44.h),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(22.r),
                  ),
                ),
                child: Text(
                  '提交',
                  style:
                      TextStyle(fontSize: 14.sp, fontWeight: FontWeight.bold),
                ),
              ),
            )),
      ),
    );
  }

  // 显示提交确认弹窗
  void _showSubmitConfirmDialog() {
    // 隐藏键盘
    FocusScope.of(context).unfocus();

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(
          '提交确认',
          style: TextStyle(
            fontSize: 16.sp,
            fontWeight: FontWeight.bold,
            color: Colors.black,
          ),
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              '请确认以下信息是否正确：',
              style: TextStyle(
                fontSize: 14.sp,
                color: Colors.grey[700],
              ),
            ),
            SizedBox(height: 12.h),
            _buildConfirmItem('合同类型', contractType),
            _buildConfirmItem('收款公司', paymentCompany),
            _buildConfirmItem('合同金额',
                '¥${(contractAmount - discountAmount).toStringAsFixed(0)}'),
            if (discountAmount > 0)
              _buildConfirmItem(
                  '优惠金额', '¥${discountAmount.toStringAsFixed(0)}'),
            _buildConfirmItem('装修类型', decorationType),
            _buildConfirmItem('开工日期', startDate),
            _buildConfirmItem('完工日期', endDate),
            _buildConfirmItem('付款比例', '${totalPercentage.toStringAsFixed(0)}%'),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: Text(
              '取消',
              style: TextStyle(
                color: Colors.grey[600],
                fontSize: 14.sp,
              ),
            ),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.of(context).pop();
              _submitNakedContract();
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.black,
              foregroundColor: Colors.white,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(8.r),
              ),
            ),
            child: Text(
              '确认提交',
              style: TextStyle(
                fontSize: 14.sp,
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
        ],
      ),
    );
  }

  // 构建确认信息项
  Widget _buildConfirmItem(String label, String value) {
    return Padding(
      padding: EdgeInsets.symmetric(vertical: 2.h),
      child: Row(
        children: [
          Text(
            '$label：',
            style: TextStyle(
              fontSize: 13.sp,
              color: Colors.grey[600],
            ),
          ),
          Expanded(
            child: Text(
              value,
              style: TextStyle(
                fontSize: 13.sp,
                color: Colors.black,
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
        ],
      ),
    );
  }

  //获取收款公司
  Future<void> _fetchPaymentCompanies() async {
    final apiManager = ApiManager();
    try {
      final response = await apiManager
          .get('/api/signature/company/list', queryParameters: {});

      if (response != null && response is List) {
        setState(() {
          companyOptions = List<Map<String, dynamic>>.from(response);
        });
      }
    } catch (e) {
      print('Failed to load payment companies: $e');
      showDialog(
        context: context,
        builder: (context) => AlertDialog(
          title: const Text('加载失败'),
          content: const Text('无法加载收款公司列表，请稍后再试。'),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text('关闭'),
            ),
          ],
        ),
      );
    }
  }

  //获取装修类型
  Future<void> _fetchHouseType(String path) async {
    try {
      final apiManager = ApiManager();
      final response = await apiManager.get(
        '/api/home/<USER>/$path',
        queryParameters: null,
      );

      if (response != null) {
        _decorationTypeList = (response['pbs_decoration_type'] as List<dynamic>)
            .map((e) => DictOption.fromJson(Map<String, dynamic>.from(e)))
            .toList();
        setState(() {});
      }
    } catch (e) {
      print(e);
    }
  }

  // 获取原始价格
  String _getOriginalPrice() {
    return contractAmount.toStringAsFixed(0);
  }

  // 刷新合同数据
  Future<void> _refreshContractData() async {
    if (widget.onContractUpdated != null) {
      await widget.onContractUpdated!();
    }
    // 刷新优惠信息
    await _getDiscountInfo(
      widget.contractData?['customerProjectServiceId'] ?? '',
      widget.contractData?['contractType'],
    );
  }

  //获取优惠申请信息
  Future<Map<String, dynamic>?> _getDiscountInfo(
      String serviceId, String contractType) async {
    try {
      final apiManager = ApiManager();
      final response = await apiManager.get(
        '/api/signature/discount/info',
        queryParameters: {'serviceId': serviceId, 'contractType': contractType},
      );
      if (response != null) {
        // 更新优惠金额和优惠内容
        setState(() {
          // 使用 discountPrice 字段作为优惠金额
          discountAmount = (response['discountPrice'] ?? 0.0).toDouble();

          // 使用 packageDescription 数组作为优惠内容
          if (response['packageDescription'] != null &&
              response['packageDescription'] is List) {
            List<dynamic> packageDescList = response['packageDescription'];
            discountDetails = packageDescList
                .map((item) {
                  if (item is Map<String, dynamic>) {
                    return item['description']?.toString() ?? '';
                  } else {
                    // 兼容旧的字符串格式
                    return item.toString();
                  }
                })
                .where((desc) => desc.isNotEmpty)
                .toList()
                .cast<String>();
          } else {
            discountDetails = [];
          }
        });
        return response;
      }
      return null;
    } catch (e) {
      debugPrint('获取优惠信息失败: $e');
      return null;
    }
  }

  //提交裸签合同
  Future<void> _submitNakedContract() async {
    // 确保键盘隐藏
    FocusScope.of(context).unfocus();

    try {
      final apiManager = ApiManager();
      // 查找收款公司ID
      String? incomeCompanyId;
      final companyList = companyOptions
          .where((option) => option['companyName'] == paymentCompany)
          .toList();
      if (companyList.isNotEmpty) {
        incomeCompanyId = companyList.first['id'];
      } else {
        // 如果没有找到匹配的公司名称，使用contractData中的默认值
        incomeCompanyId = widget.contractData?['incomeCompany']?.toString();
        if (incomeCompanyId == null) {
          throw Exception('未找到收款公司信息');
        }
      }

      // 查找装修类型ID
      String? packageGroupTypeId;
      final decorationList = _decorationTypeList
          .where((option) => option.label == decorationType)
          .toList();
      if (decorationList.isNotEmpty) {
        packageGroupTypeId = decorationList.first.value;
      } else {
        // 如果没有找到匹配的装修类型，使用contractData中的默认值
        packageGroupTypeId =
            widget.contractData?['packageGroupType']?.toString();
        if (packageGroupTypeId == null) {
          throw Exception('未找到装修类型信息');
        }
      }

      final response = await apiManager.post(
        '/api/signature/naked/sign/commit',
        data: {
          'customerProjectServiceId':
              widget.contractData?['customerProjectServiceId'] ?? '',
          'contractType': contractType == '一期合同' ? '1' : '2',
          'originalPrice': contractAmount, // 使用填写的合同金额
          'incomeCompany': incomeCompanyId, // 收款公司Id
          'packageGroupType': packageGroupTypeId, // 装修类型Id
          'contractConstructionStartDate': startDate,
          'contractConstructionEndDate': endDate,
          'payType': paymentType, // 付款类型
          'contractPaymentList': [
            {'proportion': firstPeriodPercentage / 100, 'type': '1'},
            {'proportion': secondPeriodPercentage / 100, 'type': '2'},
            {'proportion': thirdPeriodPercentage / 100, 'type': '3'},
            {'proportion': fourthPeriodPercentage / 100, 'type': '4'},
          ],
        },
      );

      if (response != null) {
        await Navigator.push(
          context,
          MaterialPageRoute(
              builder: (context) => ESignWebViewWidget(
                    esignContractId: widget.esignContractId ?? '',
                    contractType: widget.contractData?['contractType'],
                    serviceId:
                        widget.contractData?['customerProjectServiceId'] ?? '',
                  )),
        );

        // 如果签署完成，立即调用刷新回调
        if (widget.onContractUpdated != null) {
          await widget.onContractUpdated!();
        }
      }
    } catch (e) {
      print('提交裸签合同失败: $e');
      showDialog(
        context: context,
        builder: (context) => AlertDialog(
          title: const Text('提交失败'),
          content: const Text('无法提交裸签合同，请稍后再试。'),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text('关闭'),
            ),
          ],
        ),
      );
    }
  }
}
