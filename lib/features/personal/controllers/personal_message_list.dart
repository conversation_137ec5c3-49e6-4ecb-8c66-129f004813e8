import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_smarthome/core/network/api_manager.dart';
import 'package:flutter_smarthome/core/utils/hex_color.dart';
import 'package:pull_to_refresh/pull_to_refresh.dart';

class PersonalMessageListWidget extends StatefulWidget {
  const PersonalMessageListWidget({super.key});

  @override
  State<PersonalMessageListWidget> createState() =>
      _PersonalMessageListWidgetState();
}

class _PersonalMessageListWidgetState extends State<PersonalMessageListWidget> {
  final RefreshController _refreshController =
      RefreshController(initialRefresh: false);

  // 分页相关变量
  int pageNum = 1;
  final int pageSize = 10;
  int pageTotal = 0;

  // 消息数据列表
  List<MessageItem> messageList = [];

  @override
  void initState() {
    super.initState();
    _getMessageList();
  }

  @override
  void dispose() {
    _refreshController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: HexColor('#F5F5F5'),
      appBar: AppBar(
        title: const Text('消息通知'),
        backgroundColor: Colors.white,
        foregroundColor: Colors.black,
        elevation: 0,
        centerTitle: true,
      ),
      body: SmartRefresher(
        enablePullDown: true,
        enablePullUp: true,
        header: const WaterDropHeader(),
        footer: CustomFooter(
          builder: (BuildContext context, LoadStatus? mode) {
            Widget body;
            if (mode == LoadStatus.idle) {
              body = const Text("上拉加载");
            } else if (mode == LoadStatus.loading) {
              body = const CircularProgressIndicator();
            } else if (mode == LoadStatus.failed) {
              body = const Text("加载失败！点击重试！");
            } else if (mode == LoadStatus.canLoading) {
              body = const Text("松手加载更多");
            } else if (mode == LoadStatus.noMore) {
              body = const Text("没有更多数据了");
            } else {
              body = const Text("");
            }
            return SizedBox(
              height: 55.0,
              child: Center(child: body),
            );
          },
        ),
        controller: _refreshController,
        onRefresh: _onRefresh,
        onLoading: _onLoading,
        child: ListView.builder(
          padding: EdgeInsets.all(16.w),
          itemCount: messageList.length,
          itemBuilder: (context, index) {
            return _buildMessageItem(messageList[index]);
          },
        ),
      ),
    );
  }

  Widget _buildMessageItem(MessageItem message) {
    return GestureDetector(
      onTap: () => _readMessage(message.noticeId),
      child: Container(
        margin: EdgeInsets.only(bottom: 12.h),
        padding: EdgeInsets.all(16.w),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(8.r),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withValues(alpha: 0.05),
              blurRadius: 4,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // 标题行，包含标题和已读/未读状态
            Row(
              children: [
                Expanded(
                  child: Text(
                    message.title,
                    style: TextStyle(
                      fontSize: 16.sp,
                      fontWeight: FontWeight.bold,
                      color: Colors.black,
                    ),
                  ),
                ),
                // 未读小红点
                if (message.isRead == '0')
                  Container(
                    width: 8.w,
                    height: 8.w,
                    decoration: const BoxDecoration(
                      color: Colors.red,
                      shape: BoxShape.circle,
                    ),
                  ),
              ],
            ),
            SizedBox(height: 8.h),
            // 内容
            Text(
              message.content,
              style: TextStyle(
                fontSize: 14.sp,
                color: HexColor('#666666'),
                height: 1.4,
              ),
            ),
            SizedBox(height: 12.h),
            // 分割线
            Divider(
              height: 1.h,
              color: HexColor('#E5E5E5'),
            ),
            SizedBox(height: 12.h),
            // 时间和查看详情
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  message.time,
                  style: TextStyle(
                    fontSize: 12.sp,
                    color: HexColor('#999999'),
                  ),
                ),
                GestureDetector(
                  onTap: () => _onViewDetail(message),
                  child: Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Text(
                        '查看详情',
                        style: TextStyle(
                          fontSize: 12.sp,
                          color: HexColor('#999999'),
                        ),
                      ),
                      SizedBox(width: 4.w),
                      Icon(
                        Icons.arrow_forward_ios,
                        size: 12.sp,
                        color: HexColor('#999999'),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  // 下拉刷新
  void _onRefresh() async {
    pageNum = 1;
    pageTotal = 0;
    messageList.clear();
    setState(() {});
    try {
      await _getMessageList();
      _refreshController.refreshCompleted();
    } catch (e) {
      _refreshController.refreshFailed();
    }
  }

  // 上拉加载更多
  void _onLoading() async {
    // 检查是否已经到达最后一页
    if (pageNum >= pageTotal && pageTotal > 0) {
      _refreshController.loadNoData();
      return;
    }

    pageNum++;
    try {
      await _getMessageList();
      _refreshController.loadComplete();
    } catch (e) {
      _refreshController.loadFailed();
    }
  }

  void _onViewDetail(MessageItem message) {
    // 处理查看详情点击事件
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(message.title),
        content: Text(message.content),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('确定'),
          ),
        ],
      ),
    );
  }

  //获取列表数据
  Future<void> _getMessageList() async {
    try {
      final apiManager = ApiManager();
      final response = await apiManager.get(
        '/api/personal/message/list',
        queryParameters: {
          'pageNum': pageNum,
          'pageSize': pageSize,
        },
      );

      if (response != null && mounted) {
        final List<dynamic> rows = response['rows'] ?? [];
        final int currentPageTotal = response['pageTotal'] ?? 0;
        final List<MessageItem> newItems = rows
            .whereType<Map<String, dynamic>>()
            .map((e) => MessageItem.fromJson(e))
            .toList();

        setState(() {
          if (pageNum == 1) {
            messageList = newItems;
          } else {
            messageList.addAll(newItems);
          }
          pageTotal = currentPageTotal;
        });

        // 处理加载更多状态
        if (newItems.isEmpty || pageNum >= pageTotal) {
          _refreshController.loadNoData();
        }
      }
    } catch (e) {
      debugPrint('获取消息列表失败: $e');
    }
  }

  //消息已读
  Future<void> _readMessage(String noticeId) async {
    try {
      final apiManager = ApiManager();
      final response = await apiManager.post(
        '/api/personal/message/read',
        data: {
          'noticeId': noticeId,
        },
      );
      if (response != null) {
        //当前这行红点去掉
        setState(() {
          for (var item in messageList) {
            if (item.noticeId == noticeId) {
              item.isRead = '1';
              break;
            }
          }
        });
      }
    } catch (e) {
      debugPrint('消息已读失败: $e');
    }
  }
}

// 消息数据模型
class MessageItem {
  final String noticeId;
  final String title;
  final String content;
  final String time;
  final String? noticeIcon;
  final String? path;
  String isRead;

  MessageItem({
    required this.noticeId,
    required this.title,
    required this.content,
    required this.time,
    this.noticeIcon,
    this.path,
    required this.isRead,
  });

  factory MessageItem.fromJson(Map<String, dynamic> json) {
    return MessageItem(
      noticeId: json['noticeId']?.toString() ?? '',
      title: json['noticeTitle']?.toString() ?? '',
      content: json['noticeContent']?.toString() ?? '',
      time: json['createTime']?.toString() ?? '',
      noticeIcon: json['noticeIcon']?.toString(),
      path: json['path']?.toString(),
      isRead: json['isRead']?.toString() ?? '0',
    );
  }
}
