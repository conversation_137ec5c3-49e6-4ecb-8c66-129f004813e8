import 'package:flutter/material.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/services.dart';
import 'dart:io';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_smarthome/features/personal/controllers/address_list.dart';
import 'package:flutter_smarthome/core/network/api_manager.dart';
import 'package:flutter_smarthome/core/utils/cache_util.dart';
import 'package:flutter_smarthome/core/utils/custom_webview.dart';
import 'package:flutter_smarthome/core/utils/user_manager.dart';
import 'package:oktoast/oktoast.dart';
import 'package:package_info_plus/package_info_plus.dart';
import './personal_info.dart';

class PersonalSettingWidget extends StatefulWidget {
  const PersonalSettingWidget({super.key});

  @override
  State<PersonalSettingWidget> createState() => _PersonalSettingWidgetState();
}

class _PersonalSettingWidgetState extends State<PersonalSettingWidget> {
  //标题数组
  final List<String> _titleList = [
    '个人信息',
    '收货地址管理',
    '用户协议',
    '隐私政策',
    '清除缓存',
    '版本',
    '注销用户'
  ];

  String? _cacheSize; // 新增状态变量来存储缓存大小
  static const platform = MethodChannel('com.smartlife.app/login');

  @override
  void initState() {
    super.initState();
    _updateCacheSize(); // 初始化时获取缓存大小
  }

  // 新增更新缓存大小的方法
  Future<void> _updateCacheSize() async {
    final size = await CacheUtil.getCacheSize();
    setState(() {
      _cacheSize = size;
    });
  }

  // 通用的平台特定对话框方法
  Future<bool?> _showPlatformDialog({
    required String title,
    required String content,
    String cancelText = '取消',
    String confirmText = '确定',
  }) {
    if (Platform.isIOS) {
      return showCupertinoDialog<bool>(
        context: context,
        builder: (BuildContext context) {
          return CupertinoAlertDialog(
            title: Text(title),
            content: Text(content),
            actions: <Widget>[
              CupertinoDialogAction(
                onPressed: () => Navigator.of(context).pop(false),
                child: Text(cancelText),
              ),
              CupertinoDialogAction(
                onPressed: () => Navigator.of(context).pop(true),
                child: Text(confirmText),
              ),
            ],
          );
        },
      );
    } else {
      return showDialog<bool>(
        context: context,
        builder: (BuildContext context) {
          return AlertDialog(
            title: Text(title),
            content: Text(content),
            actions: <Widget>[
              TextButton(
                onPressed: () => Navigator.of(context).pop(false),
                child: Text(cancelText),
              ),
              TextButton(
                onPressed: () => Navigator.of(context).pop(true),
                child: Text(confirmText),
              ),
            ],
          );
        },
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
        backgroundColor: Colors.white,
        appBar: AppBar(
          elevation: 0,
          backgroundColor: Colors.white,
          title: const Text('设置',
              style:
                  TextStyle(color: Colors.black, fontWeight: FontWeight.bold)),
          leading: IconButton(
            icon: const Icon(Icons.arrow_back_ios, color: Colors.black),
            onPressed: () {
              Navigator.pop(context);
            },
          ),
        ),
        body: SafeArea(
          child: Column(
            children: <Widget>[
              ListView.builder(
                shrinkWrap: true,
                itemCount: _titleList.length,
                itemBuilder: (BuildContext context, int index) {
                  return ListTile(
                    title: Text(_titleList[index]),
                    trailing: Row(
                      mainAxisSize: MainAxisSize.min, // 重要：让 Row 收缩到内容大小
                      children: [
                        if (index == 4)
                          Text(_cacheSize ?? '')
                        else if (index == 5)
                          FutureBuilder<String>(
                            future: PackageInfo.fromPlatform()
                                .then((info) => info.version),
                            builder: (BuildContext context,
                                AsyncSnapshot<String> snapshot) {
                              if (snapshot.connectionState ==
                                  ConnectionState.waiting) {
                                return const SizedBox(
                                  width: 16,
                                  height: 16,
                                  child:
                                      CircularProgressIndicator(strokeWidth: 2),
                                );
                              }
                              return Text(snapshot.data ?? '');
                            },
                          ),
                        const SizedBox(width: 4),
                        const Icon(Icons.keyboard_arrow_right),
                      ],
                    ),
                    onTap: () async {
                      switch (index) {
                        case 0:
                          Navigator.push(
                              context,
                              MaterialPageRoute(
                                  builder: (context) =>
                                      const PersonalInfoWidget()));
                          break;
                        case 1:
                          Navigator.push(
                              context,
                              MaterialPageRoute(
                                  builder: (context) =>
                                      const AddressListWidget()));
                          break;
                        case 2:
                          //用户协议
                          Navigator.push(
                            context,
                            MaterialPageRoute(
                              builder: (context) => const CustomWebView(
                                url:
                                    'http://www.gazolife.com/app/html_JijiaAgreement.html',
                                title: '用户协议',
                              ),
                            ),
                          );
                          break;
                        case 3:
                          //隐私政策
                          Navigator.push(
                            context,
                            MaterialPageRoute(
                              builder: (context) => const CustomWebView(
                                url:
                                    'http://www.gazolife.com/app/jijia_PrivacyPolicy.html',
                                title: '隐私政策',
                              ),
                            ),
                          );

                          break;
                        case 4:
                          //清除缓存
                          await CacheUtil.clearCacheAndUpdate();
                          setState(() {
                            _cacheSize = ''; // 清除缓存后将缓存大小设置为空字符串
                          });
                          showToast('清除缓存成功');
                          break;
                        case 5:
                          //版本
                          showToast('当前已是最新版本');
                          break;
                        case 6:
                          //二次确定框
                          final result = await _showPlatformDialog(
                            title: '注销用户',
                            content: '确定要注销当前用户吗？',
                          );
                          if (result == true) {
                            // 处理注销逻辑
                            showToast('已提交注销请求,我们将在3个工作日内处理,请耐心等待');
                          }
                          break;
                        default:
                      }
                    },
                  );
                },
              ),
              const Spacer(),
              GestureDetector(
                onTap: () {
                  //退出登录
                  _logout();
                },
                child: Container(
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(8.r),
                    color: Colors.black,
                  ),
                  margin: EdgeInsets.fromLTRB(20.w, 0, 20.w, 20.h),
                  height: 44.h,
                  alignment: Alignment.center,
                  child:
                      const Text('退出登录', style: TextStyle(color: Colors.white)),
                ),
              )
            ],
          ),
        ));
  }

  //退出登录
  Future<void> _logout() async {
    // 显示确认对话框
    final bool? confirm = await _showPlatformDialog(
      title: '确认退出',
      content: '确定要退出登录吗？',
    );

    if (confirm != true) return;

    try {
      final apiManager = ApiManager();
      await apiManager.delete(
        '/api/login/logout',
        data: null,
      );

      // 无论接口调用成功与否，都清除本地用户状态
      await UserManager.instance.clearUser();

      // 调用原生涂鸦退出登录
      try {
        await platform.invokeMethod('tuyaLogout');
        print('涂鸦退出登录成功');
      } catch (e) {
        print('涂鸦退出登录失败: $e');
      }

      showToast('退出登录成功');

      if (context.mounted) {
        Navigator.of(context).pop(true);
      }
    } catch (e) {
      // 即使接口调用失败，也要清除本地状态（可能是token已过期）
      await UserManager.instance.clearUser();

      // 调用原生涂鸦退出登录
      try {
        await platform.invokeMethod('tuyaLogout');
        print('涂鸦退出登录成功');
      } catch (e) {
        print('涂鸦退出登录失败: $e');
      }

      showToast('退出登录成功');

      if (context.mounted) {
        Navigator.of(context).pop(true);
      }
    }
  }
}
