import 'dart:convert';
import 'package:flutter/foundation.dart';
import 'package:flutter/services.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:flutter_smarthome/core/../core/models/user_model.dart';

class UserChangeNotifier extends ChangeNotifier {
  static final UserChangeNotifier _instance = UserChangeNotifier._internal();
  factory UserChangeNotifier() => _instance;
  UserChangeNotifier._internal();

  void notifyUserChanged() {
    notifyListeners();
  }
}

class UserManagerException implements Exception {
  final String message;
  final dynamic cause;

  UserManagerException(this.message, [this.cause]);

  @override
  String toString() {
    if (cause != null) {
      return 'UserManagerException: $message\nCause: $cause';
    }
    return 'UserManagerException: $message';
  }
}

class UserManager {
  // 单例实现
  UserManager._privateConstructor() {
    _notifier = UserChangeNotifier();
    _setupMethodChannel();
  }
  static final UserManager _instance = UserManager._privateConstructor();
  static UserManager get instance => _instance;

  // 成员变量
  SharedPreferences? _prefs;
  UserModel? _user;
  late final UserChangeNotifier _notifier;
  static const String _userKey = 'user_key';
  static const String _privacyAgreedKey = 'privacy_agreed_key';
  bool _isSyncing = false;

  // Method Channel 设置
  static const _channel = MethodChannel('com.example.app/user');

  // Getters
  UserChangeNotifier get notifier => _notifier;
  UserModel? get user => _user;
  bool get isLoggedIn => _user?.accessToken?.isNotEmpty ?? false;

  // 设置 Method Channel 监听
  void _setupMethodChannel() {
    _channel.setMethodCallHandler((call) async {
      try {
        switch (call.method) {
          case 'userUpdated':
            if (call.arguments != null && !_isSyncing) {
              _isSyncing = true;
              final userMap = Map<String, dynamic>.from(call.arguments);
              await saveUser(UserModel.fromJson(userMap));
              _isSyncing = false;
            }
            break;
          case 'userCleared':
            // 只在不是同步状态时才处理清除用户的请求
            if (!_isSyncing) {
              _isSyncing = true;
              await clearUser();
              _isSyncing = false;
            }
            break;
        }
      } catch (e) {
        debugPrint('Error handling method call ${call.method}: $e');
        _isSyncing = false;
      }
    });
  }

  // 初始化
  Future<void> init() async {
    try {
      _prefs = await SharedPreferences.getInstance();
      await loadUser();

      // 异步同步到原生端，不阻塞初始化过程
      if (_user?.accessToken?.isNotEmpty ?? false) {
        _syncToNative().catchError((e) {
          debugPrint('初始化时同步到原生端失败: $e');
        });
      }
    } catch (e) {
      throw UserManagerException('Failed to initialize UserManager', e);
    }
  }

  // 加载用户
  Future<void> loadUser() async {
    _prefs ??= await SharedPreferences.getInstance();

    String? userJson = _prefs?.getString(_userKey);
    if (userJson != null) {
      try {
        Map<String, dynamic> userMap = jsonDecode(userJson);
        _user = UserModel.fromJson(userMap);
        _notifier.notifyUserChanged();
      } catch (e) {
        debugPrint('加载用户信息失败: $e');
        _user = null;
        _notifier.notifyUserChanged();
      }
    } else {
      _user = null;
      _notifier.notifyUserChanged();
    }
  }

  // 保存用户
  Future<void> saveUser(UserModel user) async {
    _prefs ??= await SharedPreferences.getInstance();

    try {
      String userJson = jsonEncode(user.toJson());
      await _prefs!.setString(_userKey, userJson);
      _user = user;
      _notifier.notifyUserChanged();

      // 异步同步到原生端，不阻塞保存过程
      if (!_isSyncing) {
        _syncToNative().catchError((e) {
          debugPrint('同步到原生端失败: $e');
          // 不抛出异常，确保用户数据保存成功
        });
      }
    } catch (e) {
      debugPrint('保存用户信息失败: $e');
      _user = null;
      _notifier.notifyUserChanged();
      throw UserManagerException('Failed to save user', e);
    }
  }

  // 清除用户
  Future<void> clearUser() async {
    _prefs ??= await SharedPreferences.getInstance();

    try {
      if (!_isSyncing) {
        await _channel.invokeMethod('clearUser');
      }
      await _prefs!.remove(_userKey);

      _user = null;
      _notifier.notifyUserChanged();
    } catch (e) {
      debugPrint('清除用户信息失败: $e');
      throw UserManagerException('Failed to clear user', e);
    }
  }

  // 更新用户
  Future<void> updateUser(void Function(UserModel) updateFn) async {
    if (_user == null) {
      throw UserManagerException('No user to update');
    }

    final updatedUser = UserModel.fromJson(_user!.toJson());
    updateFn(updatedUser);
    await saveUser(updatedUser);
  }

  // 检查用户是否已同意隐私政策
  Future<bool> hasAgreedToPrivacyPolicy() async {
    _prefs ??= await SharedPreferences.getInstance();
    return _prefs?.getBool(_privacyAgreedKey) ?? false;
  }

  // 设置用户已同意隐私政策
  Future<void> setPrivacyPolicyAgreed() async {
    _prefs ??= await SharedPreferences.getInstance();
    await _prefs!.setBool(_privacyAgreedKey, true);
  }

  // 同步到原生端
  Future<void> _syncToNative() async {
    try {
      _isSyncing = true;
      print('Syncing user to native: ${jsonEncode(_user?.toJson())}');
      final result = await _channel.invokeMethod('syncUser', _user?.toJson());
      print('Sync result: $result');
    } catch (e) {
      print('Error syncing to native: $e');
      // 不抛出异常，避免影响用户数据保存
    } finally {
      _isSyncing = false;
    }
  }
}
