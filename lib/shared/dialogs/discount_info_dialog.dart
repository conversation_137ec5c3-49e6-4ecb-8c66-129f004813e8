import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_smarthome/core/network/api_manager.dart';
import 'package:flutter_smarthome/core/utils/hex_color.dart';
import 'package:flutter_smarthome/shared/dialogs/bottom_sheet_selector.dart';

class DiscountInfoDialog extends StatefulWidget {
  final String? serviceId;
  final String? contractType;
  final String? originalPrice;
  final Function(String discountType, String? packageType,
      String discountAmount, String discountContent)? onSubmit;

  const DiscountInfoDialog({
    Key? key,
    this.serviceId,
    this.contractType,
    this.originalPrice,
    this.onSubmit,
  }) : super(key: key);

  static Future<void> show(
    String? serviceId,
    String? contractType,
    BuildContext context, {
    String? originalPrice,
    Function(String discountType, String? packageType, String discountAmount,
            String discountContent)?
        onSubmit,
    VoidCallback? onClose,
  }) {
    return showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => Padding(
        padding: EdgeInsets.only(
          bottom: MediaQuery.of(context).viewInsets.bottom,
        ),
        child: DiscountInfoDialog(
          serviceId: serviceId,
          contractType: contractType,
          originalPrice: originalPrice,
          onSubmit: onSubmit,
        ),
      ),
    ).then((_) {
      // 对话框关闭时先收起键盘，再调用onClose回调
      // 使用 WidgetsBinding 来确保在下一帧收起键盘
      WidgetsBinding.instance.addPostFrameCallback((_) {
        FocusScope.of(context).unfocus();
      });
      if (onClose != null) {
        onClose();
      }
    });
  }

  @override
  State<DiscountInfoDialog> createState() => _DiscountInfoDialogState();
}

class _DiscountInfoDialogState extends State<DiscountInfoDialog> {
  late TextEditingController _discountAmountController;
  late TextEditingController _discountContentController;

  String _selectedDiscountType = '套餐包'; // 默认选择套餐包
  String? _selectedPackageType;
  Map<String, dynamic>? _discountInfo; // 存储从API获取的优惠信息
  bool _hasExistingDiscount = false; // 新增：标记是否有已存在的优惠信息

  // 系统优惠包选项，从API获取
  List<Map<String, dynamic>> _packageOptions = [];
  List<String> _packageNames = []; // 用于显示的套餐包名称列表

  // 分别保存套餐包模式和自定义模式下的值
  String? _packageModeSelectedPackage; // 套餐包模式下选择的套餐
  String _packageModeDiscountAmount = ''; // 套餐包模式下的优惠金额
  // 套餐包模式下的优惠内容总是来自套餐包数据，不需要单独保存
  String _customModeDiscountAmount = ''; // 自定义模式下的优惠金额
  String _customModeDiscountContent = ''; // 自定义模式下的优惠内容（保留用于兼容）

  // 自定义模式下的动态优惠内容列表
  List<String> _customDiscountItems = [];
  // 自定义模式下的输入框控制器列表
  final List<TextEditingController> _customDiscountControllers = [];

  @override
  void initState() {
    super.initState();
    _discountAmountController = TextEditingController();
    _discountContentController = TextEditingController();

    // 添加监听器，当输入内容变化时更新UI
    _discountAmountController.addListener(_updateButtonState);
    _discountContentController.addListener(_updateButtonState);

    // 获取系统优惠包
    _getSystemDiscountPackages();

    // 获取优惠信息
    if (widget.serviceId != null) {
      _getDiscountInfo();
    }
  }

  void _updateButtonState() {
    // 实时保存当前模式下的输入值
    if (_selectedDiscountType == '套餐包') {
      _packageModeDiscountAmount = _discountAmountController.text;
      // 套餐包模式下绝对不保存优惠内容，因为它应该来自套餐包数据
      // 重要：在套餐包模式下，绝对不要更新自定义模式的保存值
    } else if (_selectedDiscountType == '自定义') {
      // 只有在自定义模式下才保存优惠内容
      _customModeDiscountAmount = _discountAmountController.text;
      _customModeDiscountContent = _discountContentController.text;
    }

    setState(() {
      // 触发重建，更新按钮状态
    });
  }

  // 切换优惠类型时保存和恢复各自的值
  void _switchDiscountType(String newType) {
    if (_selectedDiscountType == newType) return;

    // 暂时移除监听器，防止在设置内容时触发更新
    _discountAmountController.removeListener(_updateButtonState);
    _discountContentController.removeListener(_updateButtonState);

    // 保存当前模式的值
    if (_selectedDiscountType == '套餐包') {
      _packageModeSelectedPackage = _selectedPackageType;
      _packageModeDiscountAmount = _discountAmountController.text;
      // 套餐包模式下不保存优惠内容到自定义模式，因为它来自套餐包数据
    } else if (_selectedDiscountType == '自定义') {
      // 从自定义模式切换时才保存优惠内容
      _customModeDiscountAmount = _discountAmountController.text;
      _customModeDiscountContent = _discountContentController.text;
    }

    setState(() {
      _selectedDiscountType = newType;

      // 恢复新模式的值
      if (newType == '套餐包') {
        _selectedPackageType = _packageModeSelectedPackage;
        _discountAmountController.text = _packageModeDiscountAmount;
        // 如果有选中的套餐包，恢复其优惠内容；否则清空
        if (_packageModeSelectedPackage != null) {
          final packageIndex =
              _packageNames.indexOf(_packageModeSelectedPackage!);
          if (packageIndex >= 0 && packageIndex < _packageOptions.length) {
            // 处理系统优惠包的description字段，支持新的对象数组格式和旧的字符串格式
            final descriptionData =
                _packageOptions[packageIndex]['description'];
            String packageDescription = '';
            if (descriptionData is List) {
              // 检查是否为新的对象数组格式
              if (descriptionData.isNotEmpty && descriptionData.first is Map) {
                // 新格式：对象数组，直接提取description
                final List<Map<String, dynamic>> items = descriptionData
                    .where((item) => item is Map && item['description'] != null)
                    .map((item) => item as Map<String, dynamic>)
                    .toList();

                packageDescription = items
                    .map((item) => item['description']?.toString() ?? '')
                    .where((desc) => desc.trim().isNotEmpty)
                    .join('\n');
              } else {
                // 旧格式：字符串数组
                packageDescription = descriptionData
                    .where((item) =>
                        item != null && item.toString().trim().isNotEmpty)
                    .map((item) => item.toString())
                    .join('\n');
              }
            } else {
              // 字符串格式
              packageDescription = descriptionData?.toString() ?? '';
            }
            _discountContentController.text = packageDescription;
          } else {
            _discountContentController.text = '';
          }
        } else {
          _discountContentController.text = '';
        }
      } else {
        // 切换到自定义模式 - 使用保存的自定义内容
        _selectedPackageType = null; // 清空套餐包选择
        _discountAmountController.text = _customModeDiscountAmount;
        _discountContentController.text = _customModeDiscountContent;
        // 如果动态列表为空且有旧的优惠内容，则转换为动态列表
        if (_customDiscountItems.isEmpty &&
            _customModeDiscountContent.isNotEmpty) {
          _customDiscountItems = [_customModeDiscountContent];
          // 为优惠项创建控制器
          _customDiscountControllers.clear();
          final controller =
              TextEditingController(text: _customModeDiscountContent);
          _customDiscountControllers.add(controller);
        }
      }
    });

    // 重新添加监听器
    _discountAmountController.addListener(_updateButtonState);
    _discountContentController.addListener(_updateButtonState);
  }

  @override
  void dispose() {
    _discountAmountController.removeListener(_updateButtonState);
    _discountContentController.removeListener(_updateButtonState);
    _discountAmountController.dispose();
    _discountContentController.dispose();
    // 清理自定义优惠内容的控制器
    for (var controller in _customDiscountControllers) {
      controller.dispose();
    }
    super.dispose();
  }

  // 关闭弹窗的统一方法，先收起键盘再关闭
  void _closeDialog() {
    FocusScope.of(context).unfocus(); // 收起键盘
    Navigator.pop(context); // 关闭弹窗
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () {
        // 点击空白处收起键盘
        FocusScope.of(context).unfocus();
      },
      child: Container(
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.only(
            topLeft: Radius.circular(16.r),
            topRight: Radius.circular(16.r),
          ),
        ),
        child: SingleChildScrollView(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              _buildHeader(),
              if (_hasExistingDiscount)
                _buildExistingDiscountView()
              else
                _buildForm(),
              if (!_hasExistingDiscount) _buildBottomButtons(),
              if (_hasExistingDiscount) _buildActionButtons(),
              SizedBox(height: 40.h), // 增加底部间距，避免按钮太靠近底部
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildHeader() {
    return Container(
      padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 16.h),
      decoration: BoxDecoration(
        border: Border(
          bottom: BorderSide(
            color: HexColor('#F5F5F5'),
            width: 1,
          ),
        ),
      ),
      child: Row(
        children: [
          SizedBox(width: 24.w), // 左侧占位，与右侧关闭按钮宽度相同
          Expanded(
            child: Center(
              child: Text(
                '优惠信息',
                style: TextStyle(
                  fontSize: 16.sp,
                  fontWeight: FontWeight.bold,
                  color: Colors.black,
                ),
              ),
            ),
          ),
          GestureDetector(
            onTap: _closeDialog,
            child: Icon(
              Icons.close,
              size: 24.w,
              color: Colors.grey[600],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildExistingDiscountView() {
    if (_discountInfo == null) {
      return Container(
        padding: EdgeInsets.all(16.w),
        child: const Center(
          child: CircularProgressIndicator(),
        ),
      );
    }

    // 根据sourceType判断优惠类型
    final sourceType = _discountInfo!['sourceType']?.toString() ?? '';
    final discountTypeText = sourceType == 'system' ? '套餐包' : '自定义';

    return Container(
      // 增加高度，确保内容有足够空间
      constraints: BoxConstraints(
        minHeight: 300.h, // 设置最小高度
      ),
      child: Padding(
        padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 16.h),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildInfoRow('优惠类型:', discountTypeText),
            SizedBox(height: 16.h),
            _buildInfoRow('套餐优惠包:',
                _discountInfo!['packageName']?.toString() ?? '客户自定义优惠包'),
            SizedBox(height: 16.h),
            _buildInfoRow('优惠金额:',
                '${_formatDiscountPrice(_discountInfo!['discountPrice'])} 元'),
            SizedBox(height: 16.h),
            _buildInfoRow('优惠内容:', _formatPackageDescription()),
            SizedBox(height: 8.h),
            _buildInfoRow('审核状态:', _getConfirmStatusText()),
            SizedBox(height: 16.h),
            _buildInfoRow('是否已使用:', _getIsUsefulText()),
            SizedBox(height: 16.h),
          ],
        ),
      ),
    );
  }

  Widget _buildInfoRow(String label, String value) {
    // 特殊处理优惠内容，直接平铺显示
    if (label == '优惠内容:') {
      return Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 80.w,
            child: Text(
              label,
              style: TextStyle(
                fontSize: 14.sp,
                color: Colors.black,
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
          Expanded(
            child: Text(
              value,
              style: TextStyle(
                fontSize: 14.sp,
                color: Colors.black,
              ),
            ),
          ),
        ],
      );
    }

    // 其他信息行保持原样
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        SizedBox(
          width: 80.w,
          child: Text(
            label,
            style: TextStyle(
              fontSize: 14.sp,
              color: Colors.black,
              fontWeight: FontWeight.w500,
            ),
          ),
        ),
        Expanded(
          child: Text(
            value,
            style: TextStyle(
              fontSize: 14.sp,
              color: Colors.black,
            ),
          ),
        ),
      ],
    );
  }

  // 获取审核状态文本
  String _getConfirmStatusText() {
    final confirmStatus = _discountInfo?['confirmStatus'];
    switch (confirmStatus) {
      case '0':
        return '审核中';
      case '1':
        return '已通过';
      case '2':
        return '已拒绝';
      default:
        return '审核中';
    }
  }

  // 获取是否使用状态文本
  String _getIsUsefulText() {
    final isUseful = _discountInfo?['isUseful'];
    switch (isUseful) {
      case '0':
        return '未使用';
      case '1':
        return '已使用';
      default:
        return '未使用';
    }
  }

  // 格式化优惠价格，保留2位小数
  String _formatDiscountPrice(dynamic price) {
    if (price == null) return '0.00';

    // 转换为数字
    double priceNum = 0.0;
    if (price is num) {
      priceNum = price.toDouble();
    } else if (price is String) {
      if (price.isEmpty) {
        priceNum = 0.0;
      } else {
        priceNum = double.tryParse(price) ?? 0.0;
      }
    }

    // 格式化为2位小数
    return priceNum.toStringAsFixed(2);
  }

  // 格式化优惠内容，处理新的对象数组格式、旧的字符串数组格式和字符串格式
  String _formatPackageDescription() {
    final packageDescription = _discountInfo?['packageDescription'];
    if (packageDescription == null) return '暂无描述';

    if (packageDescription is List) {
      // 检查是否为新的对象数组格式
      if (packageDescription.isNotEmpty && packageDescription.first is Map) {
        // 新格式：对象数组，直接提取description
        final List<Map<String, dynamic>> items = packageDescription
            .where((item) => item is Map && item['description'] != null)
            .map((item) => item as Map<String, dynamic>)
            .toList();

        return items
            .map((item) => item['description']?.toString() ?? '')
            .where((desc) => desc.trim().isNotEmpty)
            .join('\n');
      } else {
        // 旧格式：字符串数组，将每个元素用换行符连接
        return packageDescription
            .where((item) => item != null && item.toString().trim().isNotEmpty)
            .map((item) => item.toString())
            .join('\n');
      }
    } else {
      // 如果是字符串，直接返回
      return packageDescription.toString().isEmpty
          ? '暂无描述'
          : packageDescription.toString();
    }
  }

  Widget _buildForm() {
    return Padding(
      padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 16.h),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildDiscountTypeSection(),
          SizedBox(height: 16.h),
          if (_selectedDiscountType == '套餐包') _buildPackageSection(),
          if (_selectedDiscountType == '套餐包') SizedBox(height: 16.h),
          _buildDiscountAmountSection(),
          SizedBox(height: 16.h),
          _buildDiscountContentSection(),
        ],
      ),
    );
  }

  Widget _buildDiscountTypeSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          '优惠类型',
          style: TextStyle(
            fontSize: 14.sp,
            color: Colors.black,
            fontWeight: FontWeight.bold,
          ),
        ),
        SizedBox(height: 8.h),
        Row(
          children: [
            Expanded(
              child: Center(
                child: _buildRadioOption('套餐包', _selectedDiscountType == '套餐包'),
              ),
            ),
            // SizedBox(width: 15.w), // 缩小间距
            Expanded(
              child: Center(
                child: _buildRadioOption('自定义', _selectedDiscountType == '自定义'),
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildRadioOption(String title, bool isSelected) {
    return GestureDetector(
      onTap: () {
        _switchDiscountType(title);
      },
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Container(
            width: 16.w,
            height: 16.w,
            decoration: BoxDecoration(
              shape: BoxShape.circle,
              border: Border.all(
                color: isSelected ? Colors.black : HexColor('#CCCCCC'),
                width: 2,
              ),
            ),
            child: isSelected
                ? Center(
                    child: Container(
                      width: 8.w,
                      height: 8.w,
                      decoration: const BoxDecoration(
                        shape: BoxShape.circle,
                        color: Colors.black,
                      ),
                    ),
                  )
                : null,
          ),
          SizedBox(width: 8.w),
          Text(
            title,
            style: TextStyle(
              fontSize: 14.sp,
              color: Colors.black,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildPackageSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          '套餐优惠包',
          style: TextStyle(
            fontSize: 14.sp,
            color: Colors.black,
            fontWeight: FontWeight.w500,
          ),
        ),
        SizedBox(height: 8.h),
        GestureDetector(
          onTap: _showPackageSelector,
          child: Container(
            width: double.infinity,
            height: 44.h,
            padding: EdgeInsets.symmetric(horizontal: 12.w),
            decoration: BoxDecoration(
              border: Border.all(color: HexColor('#E5E5E5')),
              borderRadius: BorderRadius.circular(4.r),
              color: Colors.white,
            ),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  _selectedPackageType ?? '请选择',
                  style: TextStyle(
                    fontSize: 14.sp,
                    color: _selectedPackageType != null
                        ? Colors.black
                        : HexColor('#999999'),
                  ),
                ),
                Icon(
                  Icons.keyboard_arrow_down,
                  color: HexColor('#999999'),
                  size: 20.w,
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildDiscountAmountSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          '优惠金额',
          style: TextStyle(
            fontSize: 14.sp,
            color: Colors.black,
            fontWeight: FontWeight.bold,
          ),
        ),
        SizedBox(height: 8.h),
        Container(
          height: 44.h,
          decoration: BoxDecoration(
            border: Border.all(color: HexColor('#E5E5E5')),
            borderRadius: BorderRadius.circular(4.r),
            color: Colors.white,
          ),
          child: Row(
            children: [
              Expanded(
                child: TextField(
                  controller: _discountAmountController,
                  keyboardType: TextInputType.number,
                  enabled: _selectedDiscountType != '套餐包' ||
                      _selectedPackageType == null, // 套餐包模式下选择了套餐后不可编辑
                  decoration: InputDecoration(
                    hintText: '请输入',
                    hintStyle: TextStyle(
                      color: HexColor('#999999'),
                      fontSize: 14.sp,
                    ),
                    border: InputBorder.none,
                    contentPadding: EdgeInsets.symmetric(horizontal: 12.w),
                  ),
                  style: TextStyle(
                    fontSize: 14.sp,
                    color: (_selectedDiscountType == '套餐包' &&
                            _selectedPackageType != null)
                        ? HexColor('#999999') // 不可编辑时显示灰色
                        : Colors.black,
                  ),
                ),
              ),
              Container(
                padding: EdgeInsets.symmetric(horizontal: 12.w),
                child: Text(
                  '元',
                  style: TextStyle(
                    fontSize: 14.sp,
                    color: Colors.black,
                  ),
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildDiscountContentSection() {
    // 检查是否为套餐包模式且已选择套餐（不可编辑状态）
    final bool isReadOnly =
        _selectedDiscountType == '套餐包' && _selectedPackageType != null;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          '优惠内容',
          style: TextStyle(
            fontSize: 14.sp,
            color: Colors.black,
            fontWeight: FontWeight.w500,
          ),
        ),
        SizedBox(height: 8.h),
        // 如果是自定义模式，显示动态列表
        if (_selectedDiscountType == '自定义')
          _buildCustomDiscountList()
        else
          // 套餐包模式保持原有的单个输入框
          Container(
            width: double.infinity,
            height: 80.h,
            decoration: BoxDecoration(
              border: Border.all(color: HexColor('#E5E5E5')),
              borderRadius: BorderRadius.circular(4.r),
              color: Colors.white,
            ),
            child: isReadOnly
                ? // 不可编辑时使用可滚动的文本显示
                SingleChildScrollView(
                    padding: EdgeInsets.all(12.w),
                    child: Text(
                      _discountContentController.text.isEmpty
                          ? '请输入'
                          : _discountContentController.text,
                      style: TextStyle(
                        fontSize: 14.sp,
                        color: _discountContentController.text.isEmpty
                            ? HexColor('#999999')
                            : HexColor('#999999'), // 不可编辑时显示灰色
                      ),
                    ),
                  )
                : // 可编辑时使用原来的TextField
                TextField(
                    controller: _discountContentController,
                    maxLines: null,
                    expands: true,
                    textAlignVertical: TextAlignVertical.top,
                    decoration: InputDecoration(
                      hintText: '请输入',
                      hintStyle: TextStyle(
                        color: HexColor('#999999'),
                        fontSize: 14.sp,
                      ),
                      border: InputBorder.none,
                      contentPadding: EdgeInsets.all(12.w),
                    ),
                    style: TextStyle(
                      fontSize: 14.sp,
                      color: Colors.black,
                    ),
                  ),
          ),
      ],
    );
  }

  // 构建自定义模式下的动态优惠内容列表
  Widget _buildCustomDiscountList() {
    return Container(
      constraints: BoxConstraints(
        maxHeight: 300.h, // 设置最大高度，避免内容过多时超出屏幕
      ),
      child: SingleChildScrollView(
        child: Column(
          children: [
            // 显示已添加的优惠内容列表
            if (_customDiscountControllers.isNotEmpty)
              ...List.generate(_customDiscountControllers.length, (index) {
                return Container(
                  margin: EdgeInsets.only(bottom: 8.h),
                  child: Row(
                    children: [
                      // 序号圆圈
                      Container(
                        width: 20.w,
                        height: 20.w,
                        decoration: BoxDecoration(
                          color: HexColor('#FF9500'),
                          shape: BoxShape.circle,
                        ),
                        child: Center(
                          child: Text(
                            '${index + 1}',
                            style: TextStyle(
                              fontSize: 12.sp,
                              color: Colors.white,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ),
                      ),
                      SizedBox(width: 12.w),
                      // 优惠内容输入框（只有这部分有背景色）
                      Expanded(
                        child: Container(
                          constraints: BoxConstraints(
                            minHeight: 36.h,
                          ),
                          decoration: BoxDecoration(
                            color: HexColor('#F4F5F7'),
                            borderRadius: BorderRadius.circular(4.r),
                          ),
                          padding: EdgeInsets.symmetric(
                              horizontal: 12.w, vertical: 8.h),
                          child: TextField(
                            controller: _customDiscountControllers[index],
                            maxLines: null,
                            minLines: 1,
                            decoration: InputDecoration(
                              hintText: '请输入优惠内容',
                              hintStyle: TextStyle(
                                color: HexColor('#999999'),
                                fontSize: 14.sp,
                              ),
                              border: InputBorder.none,
                              contentPadding: EdgeInsets.zero,
                              isDense: true,
                            ),
                            style: TextStyle(
                              fontSize: 14.sp,
                              color: Colors.black,
                              height: 1.2,
                            ),
                            onChanged: (value) {
                              // 实时更新对应的优惠内容
                              if (index < _customDiscountItems.length) {
                                _customDiscountItems[index] = value;
                              }
                              _updateDiscountContent();
                            },
                          ),
                        ),
                      ),
                      SizedBox(width: 12.w),
                      // 删除按钮
                      GestureDetector(
                        onTap: () => _removeDiscountItem(index),
                        child: Container(
                          padding: EdgeInsets.all(4.w),
                          child: Icon(
                            Icons.delete_outline,
                            size: 20.w,
                            color: HexColor('#999999'),
                          ),
                        ),
                      ),
                    ],
                  ),
                );
              }),
          ],
        ),
      ),
    );
  }

  Widget _buildActionButtons() {
    return Padding(
      padding: EdgeInsets.symmetric(horizontal: 16.w),
      child: Row(
        children: [
          Expanded(
            child: GestureDetector(
              onTap: _handleCancelApplication,
              child: Container(
                height: 44.h,
                decoration: BoxDecoration(
                  border: Border.all(color: Colors.black),
                  borderRadius: BorderRadius.circular(22.r),
                  color: Colors.white,
                ),
                alignment: Alignment.center,
                child: Text(
                  '取消申请',
                  style: TextStyle(
                    fontSize: 16.sp,
                    color: Colors.black,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
            ),
          ),
          SizedBox(width: 16.w),
          Expanded(
            child: GestureDetector(
              onTap: _handleReapply,
              child: Container(
                height: 44.h,
                decoration: BoxDecoration(
                  color: Colors.black,
                  borderRadius: BorderRadius.circular(22.r),
                ),
                alignment: Alignment.center,
                child: Text(
                  '重新申请',
                  style: TextStyle(
                    fontSize: 16.sp,
                    color: Colors.white,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  // 构建底部按钮区域（添加优惠按钮和提交按钮同行）
  Widget _buildBottomButtons() {
    final bool isFormValid = _isFormValid();
    final bool isCustomMode = _selectedDiscountType == '自定义';

    return Padding(
      padding: EdgeInsets.symmetric(horizontal: 16.w),
      child: Row(
        children: [
          // 添加优惠按钮（仅在自定义模式下显示）
          if (isCustomMode) ...[
            Expanded(
              child: GestureDetector(
                onTap: _addDiscountItem,
                child: Container(
                  height: 44.h,
                  decoration: BoxDecoration(
                    border: Border.all(color: HexColor('#E5E5E5')),
                    borderRadius: BorderRadius.circular(22.r),
                    color: Colors.white,
                  ),
                  alignment: Alignment.center,
                  child: Text(
                    '添加优惠',
                    style: TextStyle(
                      fontSize: 14.sp,
                      color: Colors.black,
                    ),
                  ),
                ),
              ),
            ),
            SizedBox(width: 16.w),
          ],
          // 提交按钮
          Expanded(
            child: GestureDetector(
              onTap: isFormValid ? _handleSubmit : null,
              child: Container(
                height: 44.h,
                decoration: BoxDecoration(
                  color: isFormValid ? Colors.black : HexColor('#CCCCCC'),
                  borderRadius: BorderRadius.circular(22.r),
                ),
                alignment: Alignment.center,
                child: Text(
                  '提交',
                  style: TextStyle(
                    fontSize: 16.sp,
                    color: Colors.white,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  bool _isFormValid() {
    final discountAmount = _discountAmountController.text.trim();
    final discountContent = _discountContentController.text.trim();

    // 如果选择了套餐包模式
    if (_selectedDiscountType == '套餐包') {
      // 套餐包模式：只需要验证是否选择了套餐包
      if (_selectedPackageType == null) return false;
    } else {
      // 自定义模式：优惠金额必填，优惠内容可以为空（允许没有优惠项）
      if (discountAmount.isEmpty) return false;
    }

    return true;
  }

  void _showPackageSelector() {
    if (_packageNames.isEmpty) {
      // 显示暂无可用套餐包的自定义Toast提示
      _showCustomToast('暂无可用套餐包');
      return;
    }

    BottomSheetSelector.show(
      context: context,
      title: '选择套餐包',
      options: _packageNames,
      initialSelectedIndex: _selectedPackageType != null
          ? _packageNames.indexOf(_selectedPackageType!)
          : null,
      onSelected: (index) {
        // 保存当前自定义模式的内容，防止被套餐包选择影响
        final savedCustomContent = _customModeDiscountContent;
        final savedCustomAmount = _customModeDiscountAmount;

        // 暂时移除监听器，防止自动填充时触发更新
        _discountAmountController.removeListener(_updateButtonState);
        _discountContentController.removeListener(_updateButtonState);

        setState(() {
          _selectedPackageType = _packageNames[index];
          // 自动填充优惠金额和优惠内容
          final selectedPackage = _packageOptions[index];

          // 处理系统优惠包的description字段，支持新的对象数组格式和旧的字符串格式
          String packageDescription = '';
          final descriptionData = selectedPackage['description'];
          if (descriptionData is List) {
            // 检查是否为新的对象数组格式
            if (descriptionData.isNotEmpty && descriptionData.first is Map) {
              // 新格式：对象数组，直接提取description
              final List<Map<String, dynamic>> items = descriptionData
                  .where((item) => item is Map && item['description'] != null)
                  .map((item) => item as Map<String, dynamic>)
                  .toList();

              packageDescription = items
                  .map((item) => item['description']?.toString() ?? '')
                  .where((desc) => desc.trim().isNotEmpty)
                  .join('\n');
            } else {
              // 旧格式：字符串数组
              packageDescription = descriptionData
                  .where((item) =>
                      item != null && item.toString().trim().isNotEmpty)
                  .map((item) => item.toString())
                  .join('\n');
            }
          } else {
            // 字符串格式
            packageDescription = descriptionData?.toString() ?? '';
          }

          _discountAmountController.text =
              selectedPackage['price']?.toString() ?? '';
          _discountContentController.text = packageDescription;

          // 同时更新套餐包模式下保存的值
          _packageModeSelectedPackage = _selectedPackageType;
          _packageModeDiscountAmount = _discountAmountController.text;

          // 重要：强制恢复自定义模式的内容，确保不被套餐包影响
          _customModeDiscountContent = savedCustomContent;
          _customModeDiscountAmount = savedCustomAmount;
        });

        // 重新添加监听器
        _discountAmountController.addListener(_updateButtonState);
        _discountContentController.addListener(_updateButtonState);
      },
    );
  }

  //提交事件
  void _handleSubmit() {
    if (_selectedDiscountType == '套餐包') {
      _submitSystemDiscountPackage();
    } else {
      _submitCustomDiscount();
    }
  }

  void _handleCancelApplication() {
    // 显示二次确认弹框
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Text('确认取消'),
          content: const Text('确定要取消此优惠申请吗？'),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(), // 取消操作
              child: const Text('取消'),
            ),
            TextButton(
              onPressed: () {
                Navigator.of(context).pop(); // 关闭确认弹框
                _cancelDiscountApplication(); // 调用取消申请API
              },
              child: const Text('确定'),
            ),
          ],
        );
      },
    );
  }

  void _handleReapply() {
    // 切换到填写模式，允许用户重新申请
    // 保留原有的优惠信息数据，特别是id，用于编辑功能
    final existingDiscountInfo = _discountInfo;

    setState(() {
      _hasExistingDiscount = false;
      // 不清空_discountInfo，保留id等信息用于编辑

      // 根据已有的优惠信息预填充表单
      if (existingDiscountInfo != null) {
        // 根据sourceType判断优惠类型
        final sourceType = existingDiscountInfo['sourceType']?.toString() ?? '';
        _selectedDiscountType = sourceType == 'system' ? '套餐包' : '自定义';

        // 预填充优惠金额
        final discountPrice =
            existingDiscountInfo['discountPrice']?.toString() ?? '';
        _discountAmountController.text = discountPrice;

        // 预填充优惠内容，处理新的对象数组格式、旧的字符串数组格式和字符串格式
        final packageDescriptionData =
            existingDiscountInfo['packageDescription'];
        String packageDescription = '';
        if (packageDescriptionData is List) {
          // 检查是否为新的对象数组格式
          if (packageDescriptionData.isNotEmpty &&
              packageDescriptionData.first is Map) {
            // 新格式：对象数组，直接提取description
            final List<Map<String, dynamic>> items = packageDescriptionData
                .where((item) => item is Map && item['description'] != null)
                .map((item) => item as Map<String, dynamic>)
                .toList();

            packageDescription = items
                .map((item) => item['description']?.toString() ?? '')
                .where((desc) => desc.trim().isNotEmpty)
                .join('\n');
          } else {
            // 旧格式：字符串数组，将每个元素用换行符连接
            packageDescription = packageDescriptionData
                .where(
                    (item) => item != null && item.toString().trim().isNotEmpty)
                .map((item) => item.toString())
                .join('\n');
          }
        } else if (packageDescriptionData != null) {
          // 如果是字符串，直接使用
          packageDescription = packageDescriptionData.toString();
        }
        _discountContentController.text = packageDescription;

        // 如果是系统套餐包，尝试匹配套餐包名称
        if (_selectedDiscountType == '套餐包') {
          final packageName = existingDiscountInfo['packageName']?.toString();
          if (packageName != null && _packageNames.contains(packageName)) {
            _selectedPackageType = packageName;
            _packageModeSelectedPackage = packageName;
          } else {
            _selectedPackageType = null;
            _packageModeSelectedPackage = null;
          }
          _packageModeDiscountAmount = discountPrice;
          // 套餐包模式：不设置自定义模式的内容，保持自定义内容为空或之前的值
          if (_customModeDiscountContent.isEmpty) {
            _customModeDiscountContent = ''; // 确保自定义内容为空
          }
        } else {
          // 自定义模式
          _selectedPackageType = null;
          _customModeDiscountAmount = discountPrice;
          _customModeDiscountContent = packageDescription;
          // 将优惠内容转换为动态列表，处理新的对象数组格式、旧的字符串数组格式和字符串格式
          _customDiscountControllers.clear();
          if (packageDescriptionData is List) {
            // 检查是否为新的对象数组格式
            if (packageDescriptionData.isNotEmpty &&
                packageDescriptionData.first is Map) {
              // 新格式：对象数组，直接提取description
              final List<Map<String, dynamic>> items = packageDescriptionData
                  .where((item) => item is Map && item['description'] != null)
                  .map((item) => item as Map<String, dynamic>)
                  .toList();

              _customDiscountItems = items
                  .map((item) => item['description']?.toString() ?? '')
                  .where((desc) => desc.trim().isNotEmpty)
                  .toList();
            } else {
              // 旧格式：字符串数组，直接使用
              _customDiscountItems = packageDescriptionData
                  .where((item) =>
                      item != null && item.toString().trim().isNotEmpty)
                  .map((item) => item.toString())
                  .toList();
            }
          } else if (packageDescription.isNotEmpty) {
            // 如果是字符串，按换行符分割
            _customDiscountItems = packageDescription
                .split('\n')
                .where((item) => item.trim().isNotEmpty)
                .toList();
          } else {
            _customDiscountItems = [];
          }

          // 为每个优惠项创建控制器
          for (String item in _customDiscountItems) {
            final controller = TextEditingController(text: item);
            _customDiscountControllers.add(controller);
          }
          // 套餐包模式：清空套餐包相关的保存值
          _packageModeSelectedPackage = null;
          _packageModeDiscountAmount = '';
        }
      } else {
        // 如果没有已有信息，则清空表单
        _discountAmountController.clear();
        _discountContentController.clear();
        _selectedDiscountType = '套餐包';
        _selectedPackageType = null;
      }
    });
  }

  // 更新优惠内容到主控制器
  void _updateDiscountContent() {
    _discountContentController.text = _customDiscountItems.join('\n');
    _customModeDiscountContent = _discountContentController.text;
  }

  // 添加新的优惠项
  void _addDiscountItem() {
    setState(() {
      final controller = TextEditingController();
      _customDiscountControllers.add(controller);
      _customDiscountItems.add('');
    });
  }

  // 删除优惠项
  void _removeDiscountItem(int index) {
    setState(() {
      // 释放控制器
      _customDiscountControllers[index].dispose();
      _customDiscountControllers.removeAt(index);
      _customDiscountItems.removeAt(index);
      // 更新优惠内容控制器，将所有项目合并
      _updateDiscountContent();
    });
  }

  // 显示自定义Toast提示，会显示在最上层
  void _showCustomToast(String message) {
    final overlay = Overlay.of(context);
    late OverlayEntry overlayEntry;

    overlayEntry = OverlayEntry(
      builder: (context) => Positioned(
        top: MediaQuery.of(context).size.height * 0.4,
        left: 50.w,
        right: 50.w,
        child: Material(
          color: Colors.transparent,
          child: Container(
            padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 12.h),
            decoration: BoxDecoration(
              color: Colors.black.withValues(alpha: 0.8),
              borderRadius: BorderRadius.circular(8.r),
            ),
            child: Text(
              message,
              textAlign: TextAlign.center,
              style: TextStyle(
                color: Colors.white,
                fontSize: 14.sp,
              ),
            ),
          ),
        ),
      ),
    );

    overlay.insert(overlayEntry);

    // 2秒后自动移除
    Future.delayed(const Duration(seconds: 2), () {
      overlayEntry.remove();
    });
  }

  //获取优惠申请信息
  Future<void> _getDiscountInfo() async {
    try {
      final apiManager = ApiManager();
      final response = await apiManager.get(
        '/api/signature/discount/info',
        queryParameters: {
          'serviceId': widget.serviceId,
          'contractType': widget.contractType
        },
      );

      if (response != null && mounted) {
        setState(() {
          _discountInfo = response;
          _hasExistingDiscount = true; // 有优惠信息，显示查看模式
        });
      } else if (mounted) {
        setState(() {
          _hasExistingDiscount = false; // 没有优惠信息，显示填写模式
        });
      }
    } catch (e) {
      debugPrint('获取优惠信息失败: $e');
      if (mounted) {
        setState(() {
          _hasExistingDiscount = false; // 出错时显示填写模式
        });
      }
    }
  }

  //获取系统优惠包
  Future<void> _getSystemDiscountPackages() async {
    try {
      final apiManager = ApiManager();
      final response = await apiManager.get(
        '/api/signature/discount/package/list',
        queryParameters: {'packageType': widget.contractType},
      );
      if (response != null && mounted) {
        setState(() {
          _packageOptions = (response as List<dynamic>)
              .map((e) => e as Map<String, dynamic>)
              .toList();
          _packageNames =
              _packageOptions.map((e) => e['name'] as String).toList();
        });
      }
    } catch (e) {
      debugPrint('获取系统优惠包失败: $e');
    }
  }

  //提交系统优惠包
  Future<void> _submitSystemDiscountPackage() async {
    try {
      final apiManager = ApiManager();
      final response = await apiManager.post(
        '/api/signature/apply/system/discount',
        data: {
          'id': _discountInfo?['id'] ?? '',
          'serviceId': widget.serviceId,
          'packageId':
              _packageOptions[_packageNames.indexOf(_selectedPackageType!)]
                  ['id'],
          'contractType': widget.contractType,
          'originalPrice': widget.originalPrice ?? '0',
        },
      );
      if (response != null && mounted) {
        //刷新获取优惠申请信息
        _getDiscountInfo();
      } else if (mounted) {}
    } catch (e) {
      debugPrint('提交系统优惠包失败: $e');
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('提交失败，请重试')),
        );
      }
    }
  }

  //提交自定义优惠
  Future<void> _submitCustomDiscount() async {
    try {
      final apiManager = ApiManager();

      // 确保提交时使用最新的动态列表内容，传递新的对象数组格式
      List<Map<String, dynamic>> packageDescription = [];

      if (_customDiscountItems.isNotEmpty) {
        // 使用动态列表内容，构建对象数组格式
        for (int i = 0; i < _customDiscountItems.length; i++) {
          final description = _customDiscountItems[i].trim();
          if (description.isNotEmpty) {
            packageDescription.add({
              'description': description,
            });
          }
        }
      } else if (_discountContentController.text.trim().isNotEmpty) {
        // 如果动态列表为空但主控制器有内容，使用主控制器内容
        packageDescription.add({
          'description': _discountContentController.text.trim(),
        });
      }

      final submitData = {
        'id': _discountInfo?['id'] ?? '',
        'serviceId': widget.serviceId,
        'packageId': _discountInfo?['packageId'] ?? '',
        'contractType': widget.contractType,
        'discountPrice': _discountAmountController.text.trim().isEmpty
            ? '0.0'
            : _discountAmountController.text.trim(),
        'packageDescription': packageDescription,
        'originalPrice': widget.originalPrice ?? '0',
      };

      final response = await apiManager.post(
        '/api/signature/apply/custom/discount',
        data: submitData,
      );

      if (response != null && mounted) {
        //刷新获取优惠申请信息
        _getDiscountInfo();
      }
    } catch (e) {
      debugPrint('提交自定义优惠失败: $e');
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('提交失败，请重试')),
        );
      }
    }
  }

  //取消优惠申请
  Future<void> _cancelDiscountApplication() async {
    try {
      final apiManager = ApiManager();
      final response = await apiManager.delete(
        '/api/signature/cancel/discount',
        data: {
          'id': _discountInfo?['id'] ?? '',
        },
      );
      if (response != null && mounted) {
        //刷新获取优惠申请信息
        _getDiscountInfo();
        _closeDialog();
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('取消申请成功')),
        );
      } else if (mounted) {
        _closeDialog();
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('取消失败，请重试')),
        );
      }
    } catch (e) {
      debugPrint('取消优惠申请失败: $e');
      if (mounted) {
        _closeDialog();
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('取消失败，请重试')),
        );
      }
    }
  }
}
