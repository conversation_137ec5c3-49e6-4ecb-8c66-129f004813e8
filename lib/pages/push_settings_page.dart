import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../services/jpush_service.dart';

/// 推送设置页面
class PushSettingsPage extends StatefulWidget {
  const PushSettingsPage({Key? key}) : super(key: key);

  @override
  State<PushSettingsPage> createState() => _PushSettingsPageState();
}

class _PushSettingsPageState extends State<PushSettingsPage> {
  final JPushService _jpushService = JPushService();
  final TextEditingController _aliasController = TextEditingController();
  final TextEditingController _tagController = TextEditingController();
  
  String? _registrationId;
  List<String> _tags = [];
  bool _isLoading = false;

  @override
  void initState() {
    super.initState();
    _loadPushInfo();
    _setupPushListeners();
  }

  @override
  void dispose() {
    _aliasController.dispose();
    _tagController.dispose();
    super.dispose();
  }

  /// 加载推送信息
  void _loadPushInfo() async {
    setState(() {
      _isLoading = true;
    });

    try {
      _registrationId = _jpushService.registrationId;
      _tags = await _jpushService.getAllTags();
    } catch (e) {
      _showMessage('加载推送信息失败: $e');
    }

    setState(() {
      _isLoading = false;
    });
  }

  /// 设置推送监听器
  void _setupPushListeners() {
    _jpushService.onReceiveNotification = (message) {
      _showMessage('收到通知: ${message.toString()}');
    };

    _jpushService.onOpenNotification = (message) {
      _showMessage('点击通知: ${message.toString()}');
    };

    _jpushService.onReceiveMessage = (message) {
      _showMessage('收到自定义消息: ${message.toString()}');
    };
  }

  /// 显示消息
  void _showMessage(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text(message)),
    );
  }

  /// 复制Registration ID
  void _copyRegistrationId() {
    if (_registrationId != null) {
      Clipboard.setData(ClipboardData(text: _registrationId!));
      _showMessage('Registration ID 已复制到剪贴板');
    }
  }

  /// 设置别名
  void _setAlias() async {
    final alias = _aliasController.text.trim();
    if (alias.isEmpty) {
      _showMessage('请输入别名');
      return;
    }

    final success = await _jpushService.setAlias(alias);
    if (success) {
      _showMessage('设置别名成功');
      _aliasController.clear();
    } else {
      _showMessage('设置别名失败');
    }
  }

  /// 删除别名
  void _deleteAlias() async {
    final success = await _jpushService.deleteAlias();
    if (success) {
      _showMessage('删除别名成功');
    } else {
      _showMessage('删除别名失败');
    }
  }

  /// 添加标签
  void _addTag() async {
    final tag = _tagController.text.trim();
    if (tag.isEmpty) {
      _showMessage('请输入标签');
      return;
    }

    final success = await _jpushService.addTags([tag]);
    if (success) {
      _showMessage('添加标签成功');
      _tagController.clear();
      _loadPushInfo(); // 重新加载标签列表
    } else {
      _showMessage('添加标签失败');
    }
  }

  /// 删除标签
  void _deleteTag(String tag) async {
    final success = await _jpushService.deleteTags([tag]);
    if (success) {
      _showMessage('删除标签成功');
      _loadPushInfo(); // 重新加载标签列表
    } else {
      _showMessage('删除标签失败');
    }
  }

  /// 清除所有标签
  void _clearAllTags() async {
    final success = await _jpushService.cleanTags();
    if (success) {
      _showMessage('清除所有标签成功');
      _loadPushInfo(); // 重新加载标签列表
    } else {
      _showMessage('清除所有标签失败');
    }
  }

  /// 清除所有通知
  void _clearAllNotifications() async {
    await _jpushService.clearAllNotifications();
    _showMessage('清除所有通知成功');
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('推送设置'),
        backgroundColor: Colors.blue,
        foregroundColor: Colors.white,
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : SingleChildScrollView(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Registration ID 部分
                  _buildSectionTitle('Registration ID'),
                  Card(
                    child: Padding(
                      padding: const EdgeInsets.all(16),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            _registrationId ?? '未获取到 Registration ID',
                            style: const TextStyle(fontSize: 12),
                          ),
                          const SizedBox(height: 8),
                          ElevatedButton(
                            onPressed: _copyRegistrationId,
                            child: const Text('复制 Registration ID'),
                          ),
                        ],
                      ),
                    ),
                  ),
                  
                  const SizedBox(height: 20),
                  
                  // 别名设置部分
                  _buildSectionTitle('别名设置'),
                  Card(
                    child: Padding(
                      padding: const EdgeInsets.all(16),
                      child: Column(
                        children: [
                          TextField(
                            controller: _aliasController,
                            decoration: const InputDecoration(
                              labelText: '输入别名',
                              border: OutlineInputBorder(),
                            ),
                          ),
                          const SizedBox(height: 12),
                          Row(
                            children: [
                              Expanded(
                                child: ElevatedButton(
                                  onPressed: _setAlias,
                                  child: const Text('设置别名'),
                                ),
                              ),
                              const SizedBox(width: 12),
                              Expanded(
                                child: ElevatedButton(
                                  onPressed: _deleteAlias,
                                  style: ElevatedButton.styleFrom(
                                    backgroundColor: Colors.red,
                                    foregroundColor: Colors.white,
                                  ),
                                  child: const Text('删除别名'),
                                ),
                              ),
                            ],
                          ),
                        ],
                      ),
                    ),
                  ),
                  
                  const SizedBox(height: 20),
                  
                  // 标签设置部分
                  _buildSectionTitle('标签设置'),
                  Card(
                    child: Padding(
                      padding: const EdgeInsets.all(16),
                      child: Column(
                        children: [
                          TextField(
                            controller: _tagController,
                            decoration: const InputDecoration(
                              labelText: '输入标签',
                              border: OutlineInputBorder(),
                            ),
                          ),
                          const SizedBox(height: 12),
                          Row(
                            children: [
                              Expanded(
                                child: ElevatedButton(
                                  onPressed: _addTag,
                                  child: const Text('添加标签'),
                                ),
                              ),
                              const SizedBox(width: 12),
                              Expanded(
                                child: ElevatedButton(
                                  onPressed: _clearAllTags,
                                  style: ElevatedButton.styleFrom(
                                    backgroundColor: Colors.orange,
                                    foregroundColor: Colors.white,
                                  ),
                                  child: const Text('清除所有标签'),
                                ),
                              ),
                            ],
                          ),
                          const SizedBox(height: 12),
                          if (_tags.isNotEmpty) ...[
                            const Align(
                              alignment: Alignment.centerLeft,
                              child: Text('当前标签:', style: TextStyle(fontWeight: FontWeight.bold)),
                            ),
                            const SizedBox(height: 8),
                            Wrap(
                              spacing: 8,
                              children: _tags.map((tag) => Chip(
                                label: Text(tag),
                                deleteIcon: const Icon(Icons.close, size: 18),
                                onDeleted: () => _deleteTag(tag),
                              )).toList(),
                            ),
                          ],
                        ],
                      ),
                    ),
                  ),
                  
                  const SizedBox(height: 20),
                  
                  // 其他操作
                  _buildSectionTitle('其他操作'),
                  Card(
                    child: Padding(
                      padding: const EdgeInsets.all(16),
                      child: Column(
                        children: [
                          SizedBox(
                            width: double.infinity,
                            child: ElevatedButton(
                              onPressed: _clearAllNotifications,
                              style: ElevatedButton.styleFrom(
                                backgroundColor: Colors.grey,
                                foregroundColor: Colors.white,
                              ),
                              child: const Text('清除所有通知'),
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                ],
              ),
            ),
    );
  }

  Widget _buildSectionTitle(String title) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8),
      child: Text(
        title,
        style: const TextStyle(
          fontSize: 18,
          fontWeight: FontWeight.bold,
          color: Colors.blue,
        ),
      ),
    );
  }
}
