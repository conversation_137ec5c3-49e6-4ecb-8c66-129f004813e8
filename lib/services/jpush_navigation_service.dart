import 'dart:async';
import 'dart:convert';
import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:flutter/services.dart';

/// 极光推送导航服务 - 专注于跳转逻辑，不依赖具体的 JPush API
class JPushService {
  static final JPushService _instance = JPushService._internal();
  factory JPushService() => _instance;
  JPushService._internal();

  static const String _tag = 'JPushNavigationService';

  // 跳转回调 - 统一处理跳转逻辑
  Function(String type, Map<String, dynamic> data)? onNavigate;

  // 模拟的 Registration ID
  final String _registrationId = 'mock_registration_id_12345';

  /// 初始化推送服务
  Future<void> initialize() async {
    try {
      debugPrint('$_tag: 开始初始化推送导航服务');

      // 检查是否有待处理的推送通知
      await _checkPendingNotification();

      debugPrint('$_tag: 推送导航服务初始化成功');
    } catch (e) {
      debugPrint('$_tag: 推送导航服务初始化失败: $e');
    }
  }

  /// 检查是否有待处理的推送通知
  Future<void> _checkPendingNotification() async {
    try {
      if (Platform.isAndroid) {
        // Android: 从 Intent 中获取推送数据
        await _checkAndroidPendingNotification();
      } else if (Platform.isIOS) {
        // iOS: 从 UserDefaults 中获取推送数据
        await _checkIOSPendingNotification();
      }
    } catch (e) {
      debugPrint('$_tag: 检查待处理推送失败: $e');
    }
  }

  /// 检查 Android 待处理的推送
  Future<void> _checkAndroidPendingNotification() async {
    try {
      const platform = MethodChannel('com.jiyoujiaju.jijiahui/jpush');
      final String? notificationData =
          await platform.invokeMethod('getPendingNotification');

      if (notificationData != null && notificationData.isNotEmpty) {
        final Map<String, dynamic> data = json.decode(notificationData);
        debugPrint('$_tag: 处理 Android 待处理推送: $data');
        _handleNotificationClick(data);

        // 清除已处理的数据
        await platform.invokeMethod('clearPendingNotification');
      }
    } catch (e) {
      debugPrint('$_tag: 检查 Android 待处理推送失败: $e');
    }
  }

  /// 检查 iOS 待处理的推送
  Future<void> _checkIOSPendingNotification() async {
    try {
      const platform = MethodChannel('com.jiyoujiaju.jijiahui/jpush');
      final String? notificationData =
          await platform.invokeMethod('getPendingNotification');

      if (notificationData != null && notificationData.isNotEmpty) {
        final Map<String, dynamic> data = json.decode(notificationData);
        debugPrint('$_tag: 处理 iOS 待处理推送: $data');
        _handleNotificationClick(data);

        // 清除已处理的数据
        await platform.invokeMethod('clearPendingNotification');
      }
    } catch (e) {
      debugPrint('$_tag: 检查 iOS 待处理推送失败: $e');
    }
  }

  /// 统一处理通知点击事件 - 只在 Flutter 端处理跳转
  void _handleNotificationClick(Map<String, dynamic> message) {
    try {
      debugPrint('$_tag: 处理通知点击: $message');

      // 解析附加数据
      Map<String, dynamic>? extras;

      if (Platform.isAndroid) {
        // Android 端的附加数据可能在 extras 字段中
        if (message['extras'] != null) {
          if (message['extras'] is String) {
            extras = json.decode(message['extras']);
          } else {
            extras = Map<String, dynamic>.from(message['extras']);
          }
        } else {
          extras = message;
        }
      } else if (Platform.isIOS) {
        // iOS 端的附加数据直接在 message 中
        extras = message;
      }

      if (extras != null) {
        final type = extras['type'] as String?;

        debugPrint('$_tag: 推送类型: $type, 数据: $extras');

        // 调用跳转回调
        if (onNavigate != null && type != null) {
          onNavigate!(type, extras);
        } else {
          // 默认跳转处理
          _defaultNavigationHandler(type, extras);
        }
      }
    } catch (e) {
      debugPrint('$_tag: 处理通知点击失败: $e');
    }
  }

  /// 默认跳转处理
  void _defaultNavigationHandler(String? type, Map<String, dynamic> data) {
    switch (type) {
      case 'device':
        final deviceId = data['deviceId'] as String?;
        debugPrint('$_tag: 默认处理 - 跳转到设备详情页: $deviceId');
        // 这里可以添加具体的设备页面跳转逻辑
        break;
      case 'home':
        debugPrint('$_tag: 默认处理 - 跳转到首页');
        // 这里可以添加具体的首页跳转逻辑
        break;
      case 'settings':
        debugPrint('$_tag: 默认处理 - 跳转到设置页');
        // 这里可以添加具体的设置页跳转逻辑
        break;
      default:
        debugPrint('$_tag: 默认处理 - 未知推送类型: $type');
    }
  }

  /// 获取 Registration ID
  String get registrationId => _registrationId;

  /// 手动处理推送数据 - 用于测试或手动触发
  void handlePushData(Map<String, dynamic> data) {
    _handleNotificationClick(data);
  }

  /// 模拟接收到推送通知点击
  void simulateNotificationClick(String type, Map<String, dynamic> data) {
    final message = {
      'type': type,
      ...data,
    };
    _handleNotificationClick(message);
  }

  /// 设置别名 - 模拟版
  Future<bool> setAlias(String alias) async {
    try {
      debugPrint('$_tag: 模拟设置别名: $alias');
      // 这里可以调用实际的 JPush API
      await Future.delayed(const Duration(milliseconds: 500)); // 模拟网络延迟
      debugPrint('$_tag: 设置别名成功: $alias');
      return true;
    } catch (e) {
      debugPrint('$_tag: 设置别名失败: $e');
      return false;
    }
  }

  /// 设置标签 - 模拟版
  Future<bool> setTags(List<String> tags) async {
    try {
      debugPrint('$_tag: 模拟设置标签: $tags');
      // 这里可以调用实际的 JPush API
      await Future.delayed(const Duration(milliseconds: 500)); // 模拟网络延迟
      debugPrint('$_tag: 设置标签成功: $tags');
      return true;
    } catch (e) {
      debugPrint('$_tag: 设置标签失败: $e');
      return false;
    }
  }

  /// 清除所有通知 - 模拟版
  Future<void> clearAllNotifications() async {
    try {
      debugPrint('$_tag: 模拟清除所有通知');
      // 这里可以调用实际的 JPush API
      await Future.delayed(const Duration(milliseconds: 300)); // 模拟网络延迟
      debugPrint('$_tag: 清除所有通知成功');
    } catch (e) {
      debugPrint('$_tag: 清除所有通知失败: $e');
    }
  }

  /// 测试推送跳转功能
  void testNavigation() {
    debugPrint('$_tag: 开始测试推送跳转功能');

    // 测试设备跳转
    Future.delayed(const Duration(seconds: 1), () {
      simulateNotificationClick('device', {
        'deviceId': 'test_device_001',
        'deviceName': '测试智能灯泡',
      });
    });

    // 测试首页跳转
    Future.delayed(const Duration(seconds: 3), () {
      simulateNotificationClick('home', {});
    });

    // 测试设置页跳转
    Future.delayed(const Duration(seconds: 5), () {
      simulateNotificationClick('settings', {
        'section': 'account',
      });
    });
  }
}
