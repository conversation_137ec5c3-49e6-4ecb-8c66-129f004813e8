import 'dart:async';
import 'dart:convert';
import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:jpush_flutter/jpush_flutter.dart';
import 'package:jpush_flutter/jpush_interface.dart';

/// 极光推送服务类 - 简化版，统一处理跳转逻辑
class JPushService {
  static final JPushService _instance = JPushService._internal();
  factory JPushService() => _instance;
  JPushService._internal();

  static const String _tag = 'JPushService';

  // 极光推送配置
  static const String _appKey = 'e4b1e2e32964eff17b239b67';

  JPushFlutterInterface? _jpush;
  String? _registrationId;

  // 跳转回调 - 统一处理跳转逻辑
  Function(String type, Map<String, dynamic> data)? onNavigate;

  // 简化的消息回调
  Function(Map<String, dynamic>)? onReceiveNotification;
  Function(Map<String, dynamic>)? onOpenNotification;
  Function(Map<String, dynamic>)? onReceiveMessage;

  /// 初始化极光推送
  Future<void> initialize() async {
    try {
      _jpush = JPush.newJPush();

      // 注意: dev-3.x 版本不支持 setDebugMode 方法
      debugPrint('$_tag: 调试模式已启用 (通过 kDebugMode)');

      // 初始化
      _jpush!.setup(
        appKey: _appKey,
        channel: Platform.isIOS ? 'App Store' : 'developer-default',
        production: false,
      );

      // 获取 Registration ID
      _registrationId = await _jpush!.getRegistrationID();
      debugPrint('$_tag: Registration ID: $_registrationId');

      // 设置监听器
      _setupListeners();

      // 检查是否有待处理的推送通知
      await _checkPendingNotification();

      debugPrint('$_tag: 极光推送初始化成功');
    } catch (e) {
      debugPrint('$_tag: 极光推送初始化失败: $e');
    }
  }

  /// 设置监听器
  void _setupListeners() {
    if (_jpush == null) return;

    // 接收通知回调方法
    _jpush!.addEventHandler(
      onReceiveNotification: (Map<String, dynamic> message) async {
        debugPrint('$_tag: 接收到通知: $message');
        if (onReceiveNotification != null) {
          onReceiveNotification!(message);
        }
      },

      // 点击通知回调方法 - 统一处理跳转
      onOpenNotification: (Map<String, dynamic> message) async {
        debugPrint('$_tag: 点击通知: $message');
        _handleNotificationClick(message);
        if (onOpenNotification != null) {
          onOpenNotification!(message);
        }
      },

      // 接收自定义消息回调方法
      onReceiveMessage: (Map<String, dynamic> message) async {
        debugPrint('$_tag: 接收到自定义消息: $message');
        if (onReceiveMessage != null) {
          onReceiveMessage!(message);
        }
      },
    );
  }

  /// 检查是否有待处理的推送通知
  Future<void> _checkPendingNotification() async {
    try {
      if (Platform.isAndroid) {
        // Android: 从 Intent 中获取推送数据
        await _checkAndroidPendingNotification();
      } else if (Platform.isIOS) {
        // iOS: 从 UserDefaults 中获取推送数据
        await _checkIOSPendingNotification();
      }
    } catch (e) {
      debugPrint('$_tag: 检查待处理推送失败: $e');
    }
  }

  /// 检查 Android 待处理的推送
  Future<void> _checkAndroidPendingNotification() async {
    // 这里可以添加 Android 特定的推送检查逻辑
    debugPrint('$_tag: 检查 Android 待处理推送');
  }

  /// 检查 iOS 待处理的推送
  Future<void> _checkIOSPendingNotification() async {
    // 这里可以添加 iOS 特定的推送检查逻辑
    debugPrint('$_tag: 检查 iOS 待处理推送');
  }

  /// 处理通知点击事件
  void _handleNotificationClick(Map<String, dynamic> message) {
    try {
      // 解析附加数据
      Map<String, dynamic>? extras;

      if (Platform.isAndroid) {
        // Android 端的附加数据在 extras 字段中
        if (message['extras'] != null) {
          if (message['extras'] is String) {
            extras = json.decode(message['extras']);
          } else {
            extras = Map<String, dynamic>.from(message['extras']);
          }
        }
      } else if (Platform.isIOS) {
        // iOS 端的附加数据直接在 message 中
        extras = message;
      }

      if (extras != null) {
        final type = extras['type'] as String?;

        switch (type) {
          case 'device':
            final deviceId = extras['deviceId'] as String?;
            debugPrint('$_tag: 跳转到设备详情页: $deviceId');
            // TODO: 实现跳转到设备详情页的逻辑
            break;
          case 'home':
            debugPrint('$_tag: 跳转到首页');
            // TODO: 实现跳转到首页的逻辑
            break;
          default:
            debugPrint('$_tag: 默认处理通知点击');
        }
      }
    } catch (e) {
      debugPrint('$_tag: 处理通知点击失败: $e');
    }
  }

  /// 获取 Registration ID
  String? get registrationId => _registrationId;

  /// 设置别名
  Future<bool> setAlias(String alias) async {
    try {
      if (_jpush == null) return false;

      await _jpush!.setAlias(alias);
      debugPrint('$_tag: 设置别名成功: $alias');
      return true;
    } catch (e) {
      debugPrint('$_tag: 设置别名失败: $e');
      return false;
    }
  }

  /// 删除别名
  Future<bool> deleteAlias() async {
    try {
      if (_jpush == null) return false;

      await _jpush!.deleteAlias();
      debugPrint('$_tag: 删除别名成功');
      return true;
    } catch (e) {
      debugPrint('$_tag: 删除别名失败: $e');
      return false;
    }
  }

  /// 设置标签
  Future<bool> setTags(List<String> tags) async {
    try {
      if (_jpush == null) return false;

      await _jpush!.setTags(tags);
      debugPrint('$_tag: 设置标签成功: $tags');
      return true;
    } catch (e) {
      debugPrint('$_tag: 设置标签失败: $e');
      return false;
    }
  }

  /// 添加标签
  Future<bool> addTags(List<String> tags) async {
    try {
      if (_jpush == null) return false;

      await _jpush!.addTags(tags);
      debugPrint('$_tag: 添加标签成功: $tags');
      return true;
    } catch (e) {
      debugPrint('$_tag: 添加标签失败: $e');
      return false;
    }
  }

  /// 删除标签
  Future<bool> deleteTags(List<String> tags) async {
    try {
      if (_jpush == null) return false;

      await _jpush!.deleteTags(tags);
      debugPrint('$_tag: 删除标签成功: $tags');
      return true;
    } catch (e) {
      debugPrint('$_tag: 删除标签失败: $e');
      return false;
    }
  }

  /// 清除所有标签
  Future<bool> cleanTags() async {
    try {
      if (_jpush == null) return false;

      await _jpush!.cleanTags();
      debugPrint('$_tag: 清除所有标签成功');
      return true;
    } catch (e) {
      debugPrint('$_tag: 清除所有标签失败: $e');
      return false;
    }
  }

  /// 获取所有标签
  Future<List<String>> getAllTags() async {
    try {
      if (_jpush == null) return [];

      final result = await _jpush!.getAllTags();
      debugPrint('$_tag: 获取所有标签: $result');

      // 处理返回的数据格式
      final tags = result['tags'];
      if (tags is List) {
        return List<String>.from(tags);
      }
      return [];
    } catch (e) {
      debugPrint('$_tag: 获取所有标签失败: $e');
      return [];
    }
  }

  /// 停止推送服务
  Future<void> stopPush() async {
    try {
      if (_jpush == null) return;

      await _jpush!.stopPush();
      debugPrint('$_tag: 停止推送服务');
    } catch (e) {
      debugPrint('$_tag: 停止推送服务失败: $e');
    }
  }

  /// 恢复推送服务
  Future<void> resumePush() async {
    try {
      if (_jpush == null) return;

      await _jpush!.resumePush();
      debugPrint('$_tag: 恢复推送服务');
    } catch (e) {
      debugPrint('$_tag: 恢复推送服务失败: $e');
    }
  }

  /// 清除通知
  Future<void> clearAllNotifications() async {
    try {
      if (_jpush == null) return;

      await _jpush!.clearAllNotifications();
      debugPrint('$_tag: 清除所有通知');
    } catch (e) {
      debugPrint('$_tag: 清除所有通知失败: $e');
    }
  }

  /// 设置角标数量 (仅iOS)
  Future<void> setBadge(int badge) async {
    try {
      if (_jpush == null || !Platform.isIOS) return;

      await _jpush!.setBadge(badge);
      debugPrint('$_tag: 设置角标数量: $badge');
    } catch (e) {
      debugPrint('$_tag: 设置角标数量失败: $e');
    }
  }
}
